import { BadRequestException, Body, Controller, Delete, HttpException, HttpStatus, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { FacilityService } from "../services/facility.service";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { InitiateFacilityCreationDto } from "../dto/initiate-facility-creation.dto";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { FacilityListDto } from "../dto/facility-list.dto";
import { UpdateFacilityDto } from "../dto/update-facility.dto";
import { FacilityDetailsDto } from "../dto/facility-details.dto";
import { FacilityDetailsByStaffDto } from "../dto/facility-details-by-staff.dto";
import { FacilityAvailabilityDetailsDto } from "../dto/facility-availability-details.dto";
import { AddFacilityUnavailabilityDto } from "../dto/add-facility-unavailability.dto";
import { UpdateFacilityUnavailabilityDto } from "../dto/update-facility-unavailability.dto";
import { DeleteFacilityUnavailabilityDto } from "../dto/delete-facility-unavailability.dto";
import { BulkUpdateFacilityUnavailabilityDto } from "../dto/bulk-update-facility-unavailability.dto";
import { ChangeStatusDto } from "src/organization/dto/change-status.dto";
import { ChangeStoreStatusDto } from "src/organization/dto/change-store-status.dto"
@ApiTags("Facility")
@ApiBearerAuth()
@Controller("facility")
export class FacilityController {
    constructor(private facilityService: FacilityService) {}

    @Post("/register")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Register a new Organization" })
    async registerFacility(@Body() initiateFacilityCreationDto: InitiateFacilityCreationDto): Promise<any> {
        return await this.facilityService.registerFacility(initiateFacilityCreationDto);
    }

    @Post("/list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Listing of all facilities" })
    async facilityList(@GetUser() user, @Body() facilityListDto: FacilityListDto): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.facilityService.facilityListOrganization(facilityListDto, user._id);
                break;
            case ENUM_ROLE_TYPE.SUPER_ADMIN:
                result = await this.facilityService.facilityListSuperAdmin(facilityListDto);
                break;
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                result = await this.facilityService.facilityListFrontDesk(facilityListDto, user._id);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.facilityService.facilityListWebMaster(facilityListDto, user._id);
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                result = await this.facilityService.facilityListTrainer(facilityListDto, user._id);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Facility list",
            data: result,
        };
    }

    @Patch("/update")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Update facilities" })
    async facilityUpdate(@GetUser() user, @Body() updateFacilityDto: UpdateFacilityDto): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.facilityService.facilityUpdateOrganization(updateFacilityDto, user._id);
                break;
            case ENUM_ROLE_TYPE.SUPER_ADMIN:
                result = await this.facilityService.facilityUpdateSuperAdmin(updateFacilityDto);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Facility update",
            data: result,
        };
    }

    @Post("/details")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER,ENUM_ROLE_TYPE.WEB_MASTER,ENUM_ROLE_TYPE.USER)
    @ApiOperation({ summary: "Listing of all facilities" })
    async facilityDetails(@GetUser() user, @Body() facilityDetailsDto: FacilityDetailsDto): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                let orgId = user._id;
                result = await this.facilityService.facilityDetailsOrganization(facilityDetailsDto, orgId);
                break;
            case ENUM_ROLE_TYPE.SUPER_ADMIN:
                result = await this.facilityService.facilityDetailsSuperAdmin(facilityDetailsDto);
                break;
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.USER:
                result = await this.facilityService.facilityDetailsStaff(facilityDetailsDto);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Facility details",
            data: result,
        };
    }

    @Post("/listByOrg/:organizationId")
    @ApiOperation({ summary: "Listing of all facilities" })
    async facilityListByOrg(@Param("organizationId") organizationId: string): Promise<any> {
        let result = await this.facilityService.facilityListOrg(organizationId);
        return {
            message: "Facility list",
            data: result,
        };
    }

    @Post("/listByStaffID")
    @ApiOperation({ summary: "Listing of all facilities" })
    async facilityListByStaffId(@Body() facilityDetailsByStaffDto: FacilityDetailsByStaffDto): Promise<any> {
        let result = await this.facilityService.facilityListByStaff(facilityDetailsByStaffDto);
        return {
            message: "Facility list",
            data: result,
        };
    }

    @Post("/addUnavailability")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Add facility Unavailability" })
    async addUnavailability(@GetUser() user, @Body() facilityUnavailabilityDto: AddFacilityUnavailabilityDto): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.facilityService.addUnavailabilityOrganization(facilityUnavailabilityDto, user._id);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.facilityService.addUnavailabilityWebMaster(facilityUnavailabilityDto, user._id);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Facility Unavailability Added",
            data: result,
        };
    }

    @Post("/availabilityDetails")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Facility availability details" })
    async availabilityDetails(@GetUser() user, @Body() facilityAvailabilityDto: FacilityAvailabilityDetailsDto): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.facilityService.facilityAvailabilityOrganization(facilityAvailabilityDto, user._id);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER || ENUM_ROLE_TYPE.TRAINER || ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                result = await this.facilityService.facilityAvailabilityStaff(facilityAvailabilityDto, user._id);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Facility availability details",
            data: result,
        };
    }

    @Patch("/updateUnavailability/:unavailabilityId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Update facility Unavailability" })
    async updateUnavailability(
        @Param("unavailabilityId") unavailabilityId: string,
        @GetUser() user,
        @Body() facilityUnavailabilityDto: UpdateFacilityUnavailabilityDto,
    ): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.facilityService.updateUnavailabilityOrganization(facilityUnavailabilityDto, user._id, unavailabilityId);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.facilityService.updateUnavailabilityWebMaster(facilityUnavailabilityDto, user._id, unavailabilityId);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Facility Unavailability Updated",
            data: result,
        };
    }

    @Delete("/deleteUnavailability/:unavailabilityId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Delete facility Unavailability" })
    async deleteUnavailability(
        @Param("unavailabilityId") unavailabilityId: string,
        @GetUser() user,
        @Body() deleteUnavailabilityDto: DeleteFacilityUnavailabilityDto,
    ): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.facilityService.deleteUnavailabilityOrganization(deleteUnavailabilityDto, user._id, unavailabilityId);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.facilityService.deleteUnavailabilityWebMaster(deleteUnavailabilityDto, user._id, unavailabilityId);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Facility Unavailability Deleted",
            data: result,
        };
    }

    @Patch("/bulkUpdate/Unavailability")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Update facility Unavailability" })
    async bulkUpdateUnavailability(@GetUser() user, @Body() bulkUpdateUnavailabilityDto: BulkUpdateFacilityUnavailabilityDto): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.facilityService.bulkUpdateUnavailabilityOrganization(bulkUpdateUnavailabilityDto, user._id);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.facilityService.bulkUpdateUnavailabilityWebMaster(bulkUpdateUnavailabilityDto, user._id);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Facility Unavailability Updated",
            data: result,
        };
    }

    @Patch("/status/:facilityId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Facility status update" })
    async organizationStatusUpdate(@Param("facilityId") facilityId: string, @Body() changeStatusDto: ChangeStatusDto): Promise<any> {
        let data = await this.facilityService.facilityStatusUpdate(facilityId, changeStatusDto);
        return {
            message: "Facility status updated",
            data: data,
        };
    }
    @Patch("/store-status/:facilityId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Facility  store status update" })
    async facilityStoreStatusUpdate(@Param("facilityId") facilityId: string, @Body() changeStatusDto: ChangeStoreStatusDto): Promise<any> {
        let data = await this.facilityService.facilityStoreStatusUpdate(facilityId, changeStatusDto);
        return {
            message: "Facility status updated",
            data: data,
        };
    }

    @Post("/app/details")
    @ApiOperation({ summary: "Listing of all facilities" })
    async facilityDetailsForApp(@Body() facilityDetailsDto: FacilityDetailsDto): Promise<any> {
        let orgId = facilityDetailsDto?.organizationId
        if (!orgId) {
            throw new HttpException("Organization id is required", HttpStatus.BAD_REQUEST);
        }
        let result = await this.facilityService.facilityDetailsOrganization(facilityDetailsDto, orgId);
        return {
            message: "Facility details",
            data: result,
        };
    }

}
