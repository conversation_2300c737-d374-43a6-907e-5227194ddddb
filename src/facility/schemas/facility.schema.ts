import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";

@Schema({ timestamps: true })
export class FacilityPaymentMethod {
    @Prop({ type: String, required: true })
    name: String;

    @Prop({ type: String, required: true })
    shortId: string;

    @Prop({ type: Boolean, default: false })
    isDefault?: boolean;

    @Prop({ required: false, trim: true })
    imageUrl: String;

    @Prop({ type: Boolean, default: true })
    isActive?: Boolean;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "PaymentMethod" })
    paymentMethodId: String;

    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    addedBy: String;
}

const FacilityPaymentMethodSchema = SchemaFactory.createForClass(FacilityPaymentMethod);

@Schema({ timestamps: true })
export class Facility {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: String, required: true })
    facilityName: string;

    @Prop({ type: String, unique: true, required: false, sparse: true })
    mobile: String;

    @Prop({ type: String, unique: true, required: false, sparse: true })
    email: String;

    @Prop({
        type: {
            state: { type: SchemaTypes.ObjectId, ref: "State" },
            city: { type: SchemaTypes.ObjectId, ref: "City" },
            addressLine1: { type: String, required: true },
            addressLine2: { type: String },
            postalCode: { type: Number },
        },
        required: true,
    })
    address: { state: string; city: string; addressLine1: string; addressLine2: string; postalCode: Number };

    @Prop({
        type: {
            billingName: { type: String, required: false },
            addressLine1: { type: String, required: false },
            addressLine2: { type: String, required: false },
            state: { type: SchemaTypes.ObjectId, ref: "State", required: false },
            city: { type: SchemaTypes.ObjectId, ref: "Cities", required: false },
            postalCode: { type: Number, required: false },
            gstNumber: { type: String, required: false },
        },
        required: true,
    })
    billingDetails: { billingName?: string; addressLine1?: string; addressLine2?: string; state?: string; city?: string; postalCode?: Number; gstNumber?: string };

    @Prop({ type: String, required: false })
    contactName: string;

    @Prop({ type: String, required: false })
    profilePicture: string;

    @Prop({ type: [String], required: false, default: [] })
    gallery: Array<string>;

    @Prop({ type: String, required: false })
    description: string;

    @Prop({ type: [{ type: SchemaTypes.ObjectId, ref: "Amenities" }], required: false, default: [] })
    amenities: Array<string>;

    @Prop({ type: Boolean, default: true })
    isActive: Boolean;

    @Prop({ type: Boolean, default: false })
    isStoreActive: Boolean;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;

    @Prop({ type: [FacilityPaymentMethodSchema], default: [] })
    paymentMethods: FacilityPaymentMethod[];
}

export type FacilityDocument = HydratedDocument<Facility>;
export const FacilitySchema = SchemaFactory.createForClass(Facility);
