import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
    ArrayMaxSize,
    ArrayMinSize,
    IsArray,
    IsEmail,
    IsMongoId,
    IsNotEmpty,
    IsOptional,
    IsString,
    IsUrl,
    Length,
    Matches,
    Max,
    <PERSON><PERSON>ength,
    Min,
    ValidateNested,
} from "class-validator";

class AddressDto {
    @ApiProperty({
        description: "The ID of the state where the facility is located.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "State must be a valid ObjectId" })
    state: string;

    @ApiProperty({
        description: "The ID of the city where the facility is located.",
        example: "659e8032293375c3166a99a0",
        required: true,
    })
    @IsMongoId({ message: "City must be a valid ObjectId" })
    city: string;

    @ApiProperty({
        description: "The specific Business Address or address of the facility.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: true,
    })
    @Length(2, 1000, { message: "Branch Address must be between 2 and 1000 characters" })
    addressLine1: string;

    @ApiProperty({
        description: "Address line 2 of the facility.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @Length(2, 1000, { message: "Business Address must be between 2 and 1000 characters" })
    addressLine2: string;

    @ApiProperty({
        description: "Please specify postal code of the city. || Must be a number",
        example: 122016,
        minLength: 6,
        maxLength: 6,
        required: true,
    })
    @Min(100000, { message: "Postal code must be a number" })
    @Max(999999, { message: "Invalid postal code" })
    postalCode: number;
}

export class BillingsAddressDto {
    @ApiProperty({
        description: "The billing name of the business or individual.",
        example: "HKS Manpower Pvt. Ltd.",
        minLength: 2,
        maxLength: 255,
        required: false,
    })
    @Length(2, 255, { message: "Billing name must be between 2 and 255 characters." })
    @IsOptional()
    billingName?: string;

    @ApiProperty({
        description: "The specific business address or address of the facility.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: true,
    })
    @Length(2, 1000, { message: "Address line 1 must be between 2 and 1000 characters." })
    @IsOptional()
    addressLine1: string;

    @ApiProperty({
        description: "Address line 2 of the facility.",
        example: "Near XYZ Mall",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @Length(2, 1000, { message: "Address line 2 must be between 2 and 1000 characters." })
    addressLine2?: string;

    @ApiProperty({
        description: "The ID of the state where the facility is located.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsMongoId({ message: "State ID must be a valid ObjectId." })
    @IsOptional()
    state?: string;

    @ApiProperty({
        description: "The ID of the city where the facility is located.",
        example: "659e8032293375c3166a99a0",
        required: false,
    })
    @IsMongoId({ message: "City ID must be a valid ObjectId." })
    @IsOptional()
    city?: string;

    @ApiProperty({
        description: "The postal code of the city. Must be a 6-digit number.",
        example: 122016,
        required: false,
    })
    @Min(100000, { message: "Postal code must be a 6-digit number." })
    @Max(999999, { message: "Postal code must be a 6-digit number." })
    @IsOptional()
    @Type(() => Number)
    postalCode?: number;

    @ApiProperty({
        description: "The GST number of the business. Must follow the GSTIN format.",
        example: "22AAAAA0000A1Z5",
        required: false,
    })
    @IsOptional()
    gstNumber?: string;
}

export class TimeSlots {
    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;
}

export class WorkingHours {
    @ApiProperty({
        description: "Days of the week the facility is available",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots for monday needs to be in correct format" })
    mon: TimeSlots[];

    @ApiProperty({
        description: "Days of the week the facility is available",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots for tuesday needs to be in correct format" })
    tue: TimeSlots[];

    @ApiProperty({
        description: "Days of the week the facility is available",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots for wednesday needs to be in correct format" })
    wed: TimeSlots[];

    @ApiProperty({
        description: "Days of the week the facility is available",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots for thursday needs to be in correct format" })
    thu: TimeSlots[];

    @ApiProperty({
        description: "Days of the week the facility is available",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots for friday needs to be in correct format" })
    fri: TimeSlots[];

    @ApiProperty({
        description: "Days of the week the facility is available",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots for saturday needs to be in correct format" })
    sat: TimeSlots[];

    @ApiProperty({
        description: "Days of the week the facility is available",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots for sunday needs to be in correct format" })
    sun: TimeSlots[];
}

export class UpdateFacilityDto {
    @ApiProperty({
        description: "The User ID of the organization.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Organization details is Invalid" })
    organizationId: string;

    @ApiProperty({
        description: "The id of the facility.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    // @IsOptional()
    @IsMongoId({ message: "Facility details is Invalid" })
    facilityId: string;

    @ApiProperty({
        description: "The branch name of the organization.",
        example: "Knox delhi",
        minLength: 2,
        maxLength: 1000,
        required: true,
    })
    @Length(2, 1000, { message: "Invalid branch name" })
    facilityName: string;

    @ApiProperty({
        description: "Available mobile number for the facility.",
        example: "9876543212",
        // minLength: 10,
        // maxLength: 10,
        required: true,
    })
    @IsOptional()
    //@Length(10, 10, { message: "Mobile No. must be of exactly 10 digits" })
    mobile: string;

    @ApiProperty({
        description: "The email address for the facility.",
        example: "<EMAIL>",
        maxLength: 255,
        required: true,
    })
    @IsOptional()
    @Transform(({ value }) => value.toLowerCase())
    @IsEmail({}, { message: "Invalid Email Format" })
    @MaxLength(255, { message: "Email must be less than 255 characters" })
    email: string;

    @ApiProperty({
        description: "The address of the Facility.",
        example: AddressDto,
        required: true,
    })
    @ValidateNested({ message: "Address is invalid" })
    @Type(() => AddressDto)
    address: AddressDto;

    @ApiProperty({
        description: "Billing details of the Facility.",
        example: BillingsAddressDto,
        required: true,
    })
    @ValidateNested({ message: "Billing details is invalid" })
    @IsOptional()
    @Type(() => BillingsAddressDto)
    billingDetails?: BillingsAddressDto;

    @ApiProperty({
        description: "The name of the contacted person of the facility.",
        example: "HKS",
        minLength: 2,
        maxLength: 50,
        required: false,
    })
    @IsOptional()
    @Length(2, 50, { message: "Contact Name must be between 2 and 50 characters" })
    contactName: string;

    @ApiProperty({
        description: "The profile picture URL for the facility.",
        example: "https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example.jpg",
        required: false,
    })
    @IsOptional()
    @IsUrl({}, { message: "Profile Picture must be a valid URL" })
    @IsString({ message: "Profile Picture must be a string" })
    profilePicture: string;

    @ApiProperty({
        description: "The gallery URLs for the gym.",
        example: ["https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example1.jpg", "https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example2.jpg"],
        required: false,
        type: [String], // Indicate that it's an array of strings
    })
    @IsOptional()
    @IsArray({ message: "Gallery of the facility  must be an array of URLs" })
    @IsString({ each: true })
    @IsUrl(undefined, { each: true })
    @ArrayMaxSize(8, { message: "Maximum 8 images are allowed" })
    gallery: string[];

    @ApiProperty({
        description: "Description of the facility.",
        example: "A gym is a facility equipped with fitness machines and spaces for exercise, promoting health, strength, and overall well-being.",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @Length(2, 1000, { message: "Description must be between 2 and 1000 characters" })
    description: string;

    @ApiProperty({
        description: "Amenities of the facility. || Must be an array of mongo ids",
        example: ["wifi", "steam bath", "locker"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Amenities must be an array" })
    @IsMongoId({
        each: true,
        message: "Invalid amenities",
    })
    @ArrayMinSize(1, { message: "Amenities array must contain at least one item" })
    amenities: Array<string>;

    @ApiProperty({
        description: "The Week days for staff availability",
        type: WorkingHours,
    })
    @IsOptional()
    @ValidateNested({ message: "Invalid working hours" })
    @IsNotEmpty({ message: "Working hours are required" })
    @Type(() => WorkingHours)
    workingHours: WorkingHours;
}
