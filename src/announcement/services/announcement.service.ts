import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, ObjectId } from "mongoose";
import { Announcement, AnnouncementDocument } from "../schemas/announcement.schema";
import { CreateAnnouncementDto } from "../dtos/create-announcement.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ClientDocument, Clients } from "src/users/schemas/clients.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { GetAnnouncementDto } from "../dtos/get-announcement.dto";
import { UpdateAnnouncementStatusDto } from "../dtos/update-announcement-status.dto";
import { UpdateAnnouncementDto } from "src/announcement/dtos/update-announcement.dto";
import { isAbsolute } from "path";

@Injectable()
export class AnnouncementService {
    constructor(
        @InjectModel(Announcement.name) private readonly announcementModel: Model<AnnouncementDocument>,
        @InjectModel(StaffProfileDetails.name) private readonly staffModel: Model<StaffProfileDetails>,
        @InjectModel(Clients.name) private readonly clientModel: Model<ClientDocument>,
    ) {}

    private async getOrganizationId(user: any): Promise<any> {
        const { role } = user;
        let organizationId: any;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staff = await this.staffModel.findOne({ userId: user.id }).exec();
                organizationId = staff.organizationId;
                break;
            case ENUM_ROLE_TYPE.USER:
                const client = await this.clientModel.findOne({ userId: user.id }).exec();
                organizationId = client.organizationId;
                break;
            default:
                throw new BadRequestException("Access Denied");
        }
        if (organizationId) {
            return organizationId;
        }
        throw new BadRequestException("Access Denied");
    }

    async create(createAnnouncementDto: CreateAnnouncementDto, user: any): Promise<AnnouncementDocument> {
        const organizationId = await this.getOrganizationId(user);
        const { title, subtitle, description, imageUrl } = createAnnouncementDto;

        const announcement = new this.announcementModel({
            organizationId,
            title,
            subtitle,
            description,
            imageUrl,
            isActive: false,
        });

        return announcement.save();
    }

    async getAnnouncements(getAnnouncementDto: GetAnnouncementDto): Promise<any> {
        const { active, page, pageSize, organizationId } = getAnnouncementDto;
        const query: any = {
            deletedAt: { $exists: false },
            organizationId,
        };

        if (typeof active === "boolean") {
            query.isActive = active;
        }

        const [count, announcements] = await Promise.all([
            this.announcementModel.countDocuments(query).exec(),
            this.announcementModel
                .find(query)
                .sort({ updatedAt: -1 })
                .skip((page - 1) * pageSize)
                .limit(pageSize)
                .exec(),
        ]);

        return {
            announcements,
            total: count,
        };
    }

    async getAnnouncementById(id: any): Promise<AnnouncementDocument> {
        const announcement = await this.announcementModel.findOne({ _id: id, deletedAt: { $exists: false } }).exec();
        if (!announcement) {
            throw new NotFoundException(`Announcement with not found`);
        }
        return announcement;
    }

    async updateAnnouncement(updateData: UpdateAnnouncementDto): Promise<AnnouncementDocument> {
        const { _id: id, title, subtitle, description, imageUrl } = updateData;

        const announcement = await this.announcementModel.findOne({ _id: id, deletedAt: { $exists: false } }).exec();

        if (!announcement) {
            throw new NotFoundException(`Announcement with not found`);
        }
        if (title) announcement.title = title;
        if (subtitle) announcement.subtitle = subtitle;
        if (description) announcement.description = description;
        if (imageUrl) announcement.imageUrl = imageUrl;

        await announcement.save();
        return announcement;
    }

    async updateStatusAnnouncement(id: any, updateAnnouncementStatusDto: UpdateAnnouncementStatusDto, user: any): Promise<boolean> {
        const organizationId = await this.getOrganizationId(user);
        const maxActiveAnnouncement = 3;
        if (updateAnnouncementStatusDto.isActive) {
            const activeCount = await this.announcementModel.countDocuments({
                organizationId,
                _id: { $ne: id },
                isActive: true,
                deletedAt: { $exists: false },
            });

            if (activeCount >= maxActiveAnnouncement) {
                throw new BadRequestException(`Maximum ${maxActiveAnnouncement} announcements can be active at a time`);
            }
        }

        const result = await this.announcementModel
            .findOneAndUpdate({ _id: id, deletedAt: { $exists: false }, organizationId }, { isActive: updateAnnouncementStatusDto.isActive })
            .exec();

        if (!result) {
            throw new NotFoundException(`Announcement with ID ${id} not found`);
        }
        return updateAnnouncementStatusDto.isActive;
    }

    async delete(id: any): Promise<void> {
        // const result = await this.announcementModel.findOneAndUpdate(
        //     { _id: id, deletedAt: { $exists: false } },
        //     {
        //         isActive: false,
        //         deletedAt: new Date()
        //     }
        // ).exec();
        // if (!result) {
        //     throw new NotFoundException(`Announcement with ID ${id} not found`);
        // }
        return await this.hardDelete(id);
    }

    async hardDelete(id: string): Promise<void> {
        /**
         * Handle any case where the announcement id assigned to any other model
         */
        const result = await this.announcementModel.findByIdAndDelete(id).exec();
        if (!result) {
            throw new NotFoundException(`Announcement not found`);
        }
    }
}
