import { Injectable } from "@nestjs/common";
import { TrainersListDto } from "../dto/trainers-list.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { Types } from "mongoose";
import { FacilityClientListDto } from "../dto/facility-client-list.dto";
import { RoleTableName } from "src/role/repository/entities/role.entity";

@Injectable()
export class StaffPipe {
    getStaffListPipe(trainerListDto: TrainersListDto, facilitiesList: Array<any>) {
        const userMatchCondition: any = {
        };
    
        if (typeof trainerListDto.isActive === "boolean") {
            userMatchCondition.isActive = trainerListDto.isActive;
        }
        let pipeline = [
            {
                $sort: {
                    createdAt: -1,
                },
            },
            {
                $project: {
                    userId: 1,
                    facilityId: 1,
                },
            },
            {
                $match: {
                    facilityId: {
                        $in: facilitiesList,
                    },
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [
                        {
                            $match: userMatchCondition
                        },
                        {
                            $lookup: {
                                from: RoleTableName,
                                localField: "role",
                                foreignField: "_id",
                                as: "roleDetails"
                            }
                        },
                        {
                            $match: {
                                "roleDetails.0.type": ENUM_ROLE_TYPE.TRAINER
                            }
                        }
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $project: {
                    _id: "$userId",
                    userId: 1,
                    firstName: "$userDetails.firstName",
                    lastName: "$userDetails.lastName",
                },
            },
        ] as any;
        return pipeline;
    }
    getStaffListPipev1(trainerListDto: any, facilitiesList: Array<any>, excludedTrainers: any = []) {
        const userMatchCondition: any = { role: ENUM_ROLE_TYPE.TRAINER };

        if (typeof trainerListDto.isActive === "boolean") {
            userMatchCondition.isActive = trainerListDto.isActive;
        }

        if (Array.isArray(excludedTrainers) && excludedTrainers.length) {
            userMatchCondition._id = { $nin: excludedTrainers };
        }

        let pipeline = [
            {
                $match: {
                    facilityId: { $in: facilitiesList },
                },
            },
            {
                $sort: { createdAt: -1 },
            },
            {
                $project: { userId: 1, facilityId: 1 },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [{ $match: userMatchCondition }],
                },
            },
            {
                $unwind: {
                    path: "$userDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $project: {
                    _id: "$userId",
                    userId: 1,
                    firstName: "$userDetails.firstName",
                    lastName: "$userDetails.lastName",
                },
            },
        ] as any;
        return pipeline;
    }

    getTrainersList(trainerListDto: TrainersListDto, staffId: string): Array<any> {
        const userMatchCondition: any = {
            role: ENUM_ROLE_TYPE.TRAINER,
        };

        if (typeof trainerListDto.isActive === "boolean") {
            userMatchCondition.isActive = trainerListDto.isActive;
        }
        let pipeline = [
            {
                $match: {
                    facilityId: {
                        $in: [Types.ObjectId.createFromHexString(trainerListDto.facilityId)],
                    },
                    // userId: {
                    //     $nin: [staffId],
                    // },
                },
            },
            {
                $sort: {
                    createdAt: -1,
                },
            },
            {
                $project: {
                    userId: 1,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                    pipeline: [
                        {
                            $match: userMatchCondition
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$userDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $project: {
                    userId: 1,
                    _id: "$userId",
                    firstName: "$userDetails.firstName",
                    lastName: "$userDetails.lastName",
                },
            },
        ] as any;
        return pipeline;
    }

    getTrainerClientList(facilityClientListDto: FacilityClientListDto, facilitiesId: Array<any>) {
        const pageSize = facilityClientListDto.pageSize ?? 10;
        const page = facilityClientListDto.page ?? 1;
        const skip = pageSize * (page - 1);
        let search = "";
        if (facilityClientListDto.search) {
            search = facilityClientListDto.search.trim().split(" ").join("|");
        }

        let pipeline = [
            {
                $match: {
                    facilityId: {
                        $in: facilitiesId,
                    },
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "userId",
                    foreignField: "_id",
                    as: "userDetails",
                },
            },
            {
                $unwind: {
                    path: "$userDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $facet: {
                    list: [
                        {
                            $match: {
                                $or: [
                                    {
                                        "userDetails.name": {
                                            $regex: `.*${search}.*`,
                                            $options: "i",
                                        },
                                    },
                                    {
                                        "userDetails.firstName": {
                                            $regex: `.*${search}.*`,
                                            $options: "i",
                                        },
                                    },
                                    {
                                        "userDetails.lastName": {
                                            $regex: `.*${search}.*`,
                                            $options: "i",
                                        },
                                    },
                                ],
                            },
                        },
                        {
                            $project: {
                                name: "$userDetails.name",
                                firstName: "$userDetails.firstName",
                                lastName: "$userDetails.lastName",
                                mobile: "$userDetails.mobile",
                                dob: 1,
                                photo: 1,
                                isActive: "$userDetails.isActive",
                                email:"$userDetails.email",
                                userId:"$userDetails._id"
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                    ],
                    total: [
                        {
                            $count: "total",
                        },
                    ],
                },
            },
        ];
        return pipeline;
    }
}
