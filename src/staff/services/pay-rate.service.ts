import { BadRequestException, ConflictException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { User } from "src/users/schemas/user.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { GeneralService } from "src/utils/services/general.service";
import { StaffProfileDetails } from "../schemas/staff.schema";
import { ConfigService } from "@nestjs/config";
import { MailService } from "src/mail/services/mail.service";
import { Facility } from "src/facility/schemas/facility.schema";
import { JwtService } from "@nestjs/jwt";
import { Organizations } from "src/organization/schemas/organization.schema";
import { StaffAvailability } from "../schemas/staff-availability";
import { StaffPipe } from "../pipes/staff.pipe";
import { Clients } from "src/users/schemas/clients.schema";
import { FacilityAvailability } from "src/facility/schemas/facility-availability.schema";
import { Otp } from "src/auth/schemas/otp.schema";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { CreatePayRateDto } from "src/staff/dto/create-pay-rate.dto";
import { UpdatePayRateDto } from "src/staff/dto/update-pay-rate.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetPayRatesDto } from "src/staff/dto/get-pay-rate.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { GetAllServiceCategoryByStaffDto } from "src/staff/dto/get-service-category-staff.dto";

@Injectable()
export class PayRateService {
    readonly adminFrontEndHost = this.configService.getOrThrow<string>("ADMIN_FRONTEND_APP_URL");
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(StaffProfileDetails.name) private StaffModel: Model<StaffProfileDetails>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Organizations.name) private OrganizationModel: Model<Organizations>,
        @InjectModel(StaffAvailability.name) private staffAvailabilityModel: Model<StaffAvailability>,
        @InjectModel(Otp.name) private OtpModel: Model<Otp>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailabilityModel: Model<FacilityAvailability>,
        private readonly transactionService: TransactionService,
        private readonly generalService: GeneralService,
        private readonly configService: ConfigService,
        private readonly mailService: MailService,
        private readonly staffPipe: StaffPipe,
        private JwtService: JwtService,
    ) { }
    async create(createPayRateDto: CreatePayRateDto, user): Promise<any> {
        try{
        const { userId, organizationId, serviceType, serviceCategory, appointmentType, payRate, value } = createPayRateDto;

        const createdBy = user._id;
        const role = user.role;

        let checkUser: any;

        switch (role.type) {
            case ENUM_ROLE_TYPE.SUPER_ADMIN:
            case ENUM_ROLE_TYPE.ORGANIZATION:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                checkUser = await this.StaffModel.findOne({
                    organizationId: organizationId,
                    userId: userId,
                });
                break;
            default:
                throw new BadRequestException("Access denied");
        }

        if (!checkUser) {
            throw new BadRequestException("Access denied");
        }

        const query: any = {
            userId,
            organizationId,
            serviceType,
            serviceCategory,
        };

        query.appointmentType = appointmentType;
        const existingPayRate = await this.PayRateModel.findOne(query);

        if (existingPayRate) {
            throw new ConflictException("Specialization for the given service already exists");
        }

        const payRatePayload: any = {
            userId,
            organizationId,
            serviceType,
            serviceCategory,
            payRate,
            value:value?value:"",
            createdBy,
        };

        payRatePayload.appointmentType = appointmentType;
        const payRateData = await this.PayRateModel.create(payRatePayload);
        return payRateData;
    }
    catch (error) {
        throw new BadRequestException(error.message);
    }
    }

    async findById(payRateId: string): Promise<any> {
        const payRate = await this.PayRateModel.aggregate([
            {
                $match: {
                    _id: new Types.ObjectId(payRateId),
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceCategory",
                    foreignField: "_id",
                    as: "result",
                },
            },
            {
                $unwind: "$result",
            },
            {
                $unwind: {
                    path: "$result.appointmentType",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    appointmentTypeCheck: {
                        $cond: {
                            if: { $eq: ["$serviceType", ClassType.PERSONAL_APPOINTMENT] },
                            then: { $eq: ["$appointmentType", "$result.appointmentType._id"] },
                            else: true,
                        },
                    },
                },
            },
            {
                $match: {
                    appointmentTypeCheck: true,
                },
            },
            {
                $project: {
                    serviceType: 1,
                    serviceCategory: 1,
                    appointmentType: 1,
                    payRate: 1,
                    value: 1,
                    serviceCategoryName: "$result.name",
                    appointmentTypeName: {
                        $cond: {
                            if: { $eq: ["$serviceType", ClassType.PERSONAL_APPOINTMENT] },
                            then: "$result.appointmentType.name",
                            else: null,
                        },
                    },
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
        ]).exec();

        if (payRate.length === 0) {
            throw new NotFoundException(`Specilization  for Staff with ID ${payRateId} not found`);
        }
        return {
            message: "Specilization fetched successfully",
            data: payRate[0],
        };
    }

    async getAllClassType(payRateId: string): Promise<any> {
        const clssType = await this.PayRateModel.distinct("serviceType",{userId: new Types.ObjectId(payRateId)})
        return {
            message: "Class Type fetched successfully",
            data: clssType,
        };
    }

    async getAllServiceCategoryByStaff(getAllServiceCategoryByStaff:GetAllServiceCategoryByStaffDto,staffId:string): Promise<any> {
        let {page, pageSize,serviceType } = getAllServiceCategoryByStaff;
        if (!staffId) {
            throw new BadRequestException("Staff ID is required");
        }
        const query = {};
        if(serviceType){
            query['serviceType'] = {$in:serviceType}
        }
        if(staffId){
            query['userId'] = new Types.ObjectId(staffId)
        }
        if (isNaN(page) || page < 1) {
            page = 1;
        }
        if (isNaN(pageSize) || pageSize < 1) {
            pageSize = 10;
        }
        const skip = (page - 1) * pageSize;

        const result = await this.PayRateModel.aggregate([
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceCategory",
                    foreignField: "_id",
                    as: "result",
                },
            },
            {
                $unwind: "$result",
            },
            {
                $group: {
                  _id: "$serviceCategory", 
                  serviceCategoryName: { $first: "$result.name" }, 
                  serviceType: { $first: "$result.classType" }, 
                  payRates: { $push: "$_id" }
                }
              },
              {
                $project: {
                  _id: 0, 
                  serviceId: "$_id", 
                  serviceCategoryName: 1,
                  serviceType: 1,
                  payRateId: "$payRates"
                }
              },
              {
                $facet: {
                  metadata: [{ $count: "total" }],
                  data: [{ $sort: { createdAt: -1 } }, { $skip: skip }, { $limit: pageSize }],
                }
              }
            ]).exec();

        const total = result[0]?.metadata[0]?.total || 0;
        const data = result[0]?.data || [];

        return { message: "All Service Category fetched successfully", total, data };
    }

    async findAll(getPayRatesDto: GetPayRatesDto): Promise<any> {
        let { staffId, page, pageSize,serviceType,serviceCategory,appointmentType } = getPayRatesDto;
        const query = {};
        if(serviceType){
            query['serviceType'] = serviceType
        }
        if(staffId){
            query['userId'] = new Types.ObjectId(staffId)
        }
        if(serviceCategory){
            query['serviceCategory'] = new Types.ObjectId(serviceCategory)
        }
        if(appointmentType){
            query['appointmentType'] = new Types.ObjectId(appointmentType)
        }
        if (isNaN(page) || page < 1) {
            page = 1;
        }
        if (isNaN(pageSize) || pageSize < 1) {
            pageSize = 10;
        }
        const skip = (page - 1) * pageSize;

        const result = await this.PayRateModel.aggregate([
            {
                $match: query,
            },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceCategory",
                    foreignField: "_id",
                    as: "result",
                },
            },
            {
                $unwind: "$result",
            },
            {
                $unwind: {
                    path: "$result.appointmentType",
                    preserveNullAndEmptyArrays: true, // Ensures no error when no appointment types exist
                },
            },
            { 
                $match:{
                    $expr:{$or:[{$eq:['$result.appointmentType._id', "$appointmentType"]}]}} 
                },
            {
                $lookup: {
                  from: "users", 
                  localField: "userId", 
                  foreignField: "_id", 
                  as: "userDetails"
                }
              },
              { $unwind: { path: "$userDetails", preserveNullAndEmptyArrays: true } }, 
              {
                $project: {
                    staffName: { 
                        $concat: [
                          { $ifNull: ["$userDetails.firstName", ""] },
                          " ",
                          { $ifNull: ["$userDetails.lastName", ""] }
                        ]
                      },
                  serviceType: 1,
                  serviceCategory: 1,
                  appointmentType: 1,
                  payRate: 1,
                  value: 1,
                  createdAt: 1,
                  updatedAt: 1,
                  serviceCategoryName: "$result.name",
                  appointmentTypeName:"$result.appointmentType.name"
                }
              },
              {
                $facet: {
                  metadata: [{ $count: "total" }],
                  data: [{ $sort: { createdAt: -1 } }, { $skip: skip }, { $limit: pageSize }],
                }
              }
            ]).exec();
        const total = result[0]?.metadata[0]?.total || 0;
        const data = result[0]?.data || [];

        return { message: "Specilization fetched successfully", total, data };
    }

    async update(id: string, updatePayRateDto: UpdatePayRateDto, user): Promise<PayRate> {
        const { serviceType, serviceCategory, appointmentType, payRate, value } = updatePayRateDto;
        const updatedPayRate = await this.PayRateModel.findById(id).exec();
        if (!updatedPayRate) {
            throw new BadRequestException("Specilization not found");
        }

        if (serviceType) updatedPayRate.serviceType = serviceType;
        if (serviceCategory) updatedPayRate.serviceCategory = serviceCategory;

        if (appointmentType && serviceType === ClassType.PERSONAL_APPOINTMENT) {
            updatedPayRate.appointmentType = appointmentType;
        } else if (serviceType === ClassType.PERSONAL_APPOINTMENT) {
            delete updatedPayRate.appointmentType; // Remove the key
        }

        if (payRate !== undefined) updatedPayRate.payRate = payRate;
        updatedPayRate.value = value ?? "";

        await updatedPayRate.save();
        return updatedPayRate;
    }

    async delete(id: string): Promise<PayRate> {
        const deletedPayRate = await this.PayRateModel.findByIdAndDelete(id).exec();

        if (!deletedPayRate) {
            throw new NotFoundException(`Specilization with ID ${id} not found`);
        }

        return deletedPayRate;
    }
}
