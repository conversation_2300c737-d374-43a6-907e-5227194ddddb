import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsDateString, IsArray, ValidateNested, IsMongoId, IsString, Matches, IsEnum } from "class-validator";
import { Types } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";

export class GetStaffAvailabilityDto {
    @ApiProperty({
        description: "Id of the trainer",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "trainerId is required" })
    @IsString()
    trainerId: string;

    @ApiProperty({
        description: "Id of the Branch where the trainer is assigned.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "facilityId is required" })
    @IsString()
    facilityId: string;

    // @ApiProperty({
    //     description: "Array of ClassTypes for Availability",
    //     enum: ClassType,
    //     isArray: true,
    //     example: [ClassType.PERSONAL_APPOINTMENT, ClassType.CLASSES],
    //     required: true,
    // })
    // @IsArray({ message: "ClassType must be an array of valid enum values" }) 
    // @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    // @IsNotEmpty({ message: "ClassType of Availability is required" })
    // classType: ClassType[];

    @ApiProperty({
        description: "Start date of availability",
        example: "2024-09-15T00:00:00Z",
    })
    @IsNotEmpty({ message: "Start date is required" })
    startDate: Date;

    @ApiProperty({
        description: "End date of availability",
        example: "2024-09-15T00:00:00Z",
    })
    @IsNotEmpty({ message: "End date is required" })
    endDate: Date;

    // @ApiProperty({
    //     description: "Array of ClassTypes for Availability",
    //     enum: ClassType,
    //     example: ClassType.PERSONAL_APPOINTMENT,
    //     required: false,
    // })
    // @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    // @IsOptional()
    // classType?: ClassType;

    // @ApiProperty({
    //     description: "The IDs of the PayRates for the Staff",
    //     example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41fe"],
    //     isArray: true,
    //     required: false,
    // })
    // @IsArray({ message: "payRateIds must be an array of ObjectIds" })
    // @IsMongoId({ each: true, message: "Each payRate Id must be a valid ObjectId" })
    // @IsOptional()
    // payRateIds?: string[];

    @ApiProperty({
        description: 'Start time for availability on specified days (HH:mm)',
        example: '08:00',
    })
    @IsOptional()
    @IsString({ message: 'From time must be a string' })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'From time must be in the format HH:mm' })
    from?: string;

    @ApiProperty({
        description: 'End time for availability on specified days (HH:mm)',
        example: '17:00',
    })
    @IsOptional()
    @IsString({ message: 'To time must be a string' })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'To time must be in the format HH:mm' })
    to?: string;

    @ApiProperty({
        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    @IsNotEmpty({ message: "Date range is required" })
    dateRange: DateRange;
}