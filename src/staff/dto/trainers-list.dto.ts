import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON>rray, IsEnum, IsMongoId, IsNotEmpty, IsOptional,IsBoolean } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class TrainersListDto {
    @ApiProperty({
        description: "The ID of the facility",
        example: "66cecb432351713ae4447a6b",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Required valid facility details" })
    facilityId: string;

    @ApiProperty({
        description: "Active staff list",
        type: Boolean,
        example: false,
    })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
    // @ApiProperty({
    //     description: "Array of ClassTypes for Availability",
    //     enum: ClassType,
    //     isArray: true,
    //     example: [ClassType.PERSONAL_APPOINTMENT, ClassType.CLASSES],
    //     required: true,
    // })
    // @IsArray({ message: "ClassType must be an array of valid enum values" })
    // @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    // @IsNotEmpty({ message: "ClassType must be provided" })
    // classType: ClassType[];
}
