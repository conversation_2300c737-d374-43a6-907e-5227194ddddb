import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PayRates } from "src/utils/enums/pay-rate.type.enum";

export class CreatePayRateDto {
    @ApiProperty({
        description: "The ID of the user",
        example: "66cecb432351713ae4447a6b",
    })
    @IsNotEmpty({ message: "User ID is required" })
    @IsMongoId({ message: "User ID must be a string" })
    userId: string;

    @ApiProperty({
        description: "The ID of the Organization",
        example: "66cedf7a731d1269a4157a2d",
    })
    @IsNotEmpty({ message: "Organization ID is required" })
    @IsMongoId({ message: "Organization ID must be a string" })
    organizationId: string;

    @ApiProperty({
        description: "Type of the Service",
        example: ClassType.PERSONAL_APPOINTMENT,
        enum: ClassType,
        required: true,
    })
    @IsEnum(ClassType, { message: "Service Type must be a valid enum value" })
    @IsNotEmpty({ message: "Service Type is required" })
    serviceType: ClassType;

    @ApiProperty({
        description: "The ID of the Service category",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Service Category ID must be a valid ObjectId" })
    @IsNotEmpty({ message: "Service Category is required" })
    serviceCategory: string;

    @ApiProperty({
        description: "The ID of the Appointment Type",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsMongoId({ message: "Appointment Type ID must be a valid ObjectId" })
    @IsOptional()
    appointmentType?: string;

    @ApiProperty({
        description: "Pay Rate Type of the Trainer.",
        example: PayRates.NO_PAY,
        enum: PayRates,
        required: true,
    })
    @IsNotEmpty({ message: "Pay Rate is required" })
    @IsEnum(PayRates, { message: "Pay Rate Type must be a valid enum value" })
    payRate: PayRates;

    @ApiProperty({
        description: "Value of Pay Rate",
        example: "40",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Value must be a string" })
    value?: string;
}
