import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Document } from 'mongoose';
import { UpdateResult} from 'mongodb';
import {
    IDatabaseCreateOptions,
    IDatabaseFindAllOptions,
    IDatabaseGetTotalOptions,
    IDatabaseCreateManyOptions,
    IDatabaseSaveOptions,
    IDatabaseDeleteManyOptions,
    IDatabaseFindOneOptions,
    IDatabaseOptions,
    IDatabaseUpdateManyOptions,
    IDatabaseUpdateOptions,
} from 'src/common/database/interfaces/database.interface';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';
import { PermissionUpdateRequestDto } from 'src/policy/dtos/request/permission.update.request.dto';
import { PermissionGetResponseDto } from 'src/policy/dtos/response/permission.get.response.dto';
import { PermissionListResponseDto } from 'src/policy/dtos/response/permission.list.response.dto';
import {
    PermissionDocument,
    PermissionEntity,
} from 'src/policy/repository/entities/permission.entity';
import { PermissionRepository } from 'src/policy/repository/repositories/permission.repository';
import { Types } from 'mongoose';

@Injectable()
export class PermissionService {
    constructor(private readonly permissionRepository: PermissionRepository) {}

    async findAll(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<PermissionDocument[]> {
        return this.permissionRepository.findAll(find, options);
    }

    async getTotal(
        find?: Record<string, any>,
        options?: IDatabaseGetTotalOptions
    ): Promise<number> {
        return this.permissionRepository.getTotal(find, options);
    }

    async findAllActive(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<PermissionDocument[]> {
        return this.permissionRepository.findAll(
            { ...find, isActive: true },
            options
        );
    }

    async findOneById(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<PermissionDocument> {
        return this.permissionRepository.findOneById(_id, options);
    }

    async findOne(
        find: Record<string, any>,
        options?: IDatabaseFindOneOptions
    ): Promise<PermissionDocument> {
        return this.permissionRepository.findOne(find, options);
    }

    async findOneByName(
        name: string,
        options?: IDatabaseFindOneOptions
    ): Promise<PermissionDocument> {
        return this.permissionRepository.findOne({ name }, options);
    }

    async existByName(
        name: string,
        options?: IDatabaseOptions
    ): Promise<boolean> {
        return this.permissionRepository.exists(
            { name },
            options
        );
    }

    async create(
        { name, description, type, isDelegated }: PermissionCreateRequestDto,
        options?: IDatabaseCreateOptions
    ): Promise<PermissionDocument> {
        const create: PermissionEntity = new PermissionEntity();
        create.name = name;
        create.description = description;
        create.type = type;
        create.isActive = true;
        create.isDelegated = isDelegated;

        return this.permissionRepository.create<PermissionEntity>(create, options);
    }

    async update(
        repository: PermissionDocument,
        { description, type, isDelegated }: PermissionUpdateRequestDto,
        options?: IDatabaseSaveOptions
    ): Promise<PermissionDocument> {
        repository.description = description;
        repository.type = type;
        repository.isDelegated = isDelegated;

        return this.permissionRepository.save(repository, options);
    }

    async updateOne(
        find: Record<string, any>,
        data: Partial<PermissionEntity>,
        options?: IDatabaseUpdateOptions
    ): Promise<PermissionEntity> {
        return this.permissionRepository.update(find, data, options);
    }

    async updateMany(
        find: Record<string, any>,
        data: Partial<PermissionEntity>,
        options?: IDatabaseUpdateManyOptions
    ): Promise<UpdateResult<PermissionEntity>> {
        return this.permissionRepository.updateMany<Partial<PermissionEntity>>(find, data, options);
    }

    async active(
        repository: PermissionDocument,
        options?: IDatabaseSaveOptions
    ): Promise<PermissionDocument> {
        repository.isActive = true;
        return this.permissionRepository.save(repository, options);
    }

    async inactive(
        repository: PermissionDocument,
        options?: IDatabaseSaveOptions
    ): Promise<PermissionDocument> {
        repository.isActive = false;
        return this.permissionRepository.save(repository, options);
    }

    async deleteMany(
        find: Record<string, any>,
        options?: IDatabaseDeleteManyOptions
    ): Promise<boolean> {
        await this.permissionRepository.deleteMany(find, options);
        return true;
    }

    async createMany(
        data: PermissionCreateRequestDto[],
        options?: IDatabaseCreateManyOptions
    ): Promise<boolean> {
        const create: PermissionEntity[] = data.map(({ name, type, isDelegated }) => {
            const entity: PermissionEntity = new PermissionEntity();
            entity.name = name;
            entity.type = type;
            entity.isActive = true;
            entity.isDelegated = isDelegated;
            return entity;
        });

        await this.permissionRepository.createMany<PermissionEntity>(create, options);
        return true;
    }

    async aggregate<T>(
        pipelines: any[],
        options?: IDatabaseOptions
    ): Promise<T[]> {
        return this.permissionRepository.aggregate(pipelines, options);
    }

    mapList(permissions: PermissionDocument[] | PermissionEntity[]): PermissionListResponseDto[] {
        return plainToInstance(
            PermissionListResponseDto,
            permissions.map((e: PermissionDocument | PermissionEntity) =>
                e instanceof Document ? e.toObject() : e
            )
        );
    }

    mapGet(permission: PermissionDocument | PermissionEntity): PermissionGetResponseDto {
        return plainToInstance(
            PermissionGetResponseDto,
            permission instanceof Document ? permission.toObject() : permission
        );
    }
}
