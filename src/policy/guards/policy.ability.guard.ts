import {
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_POLICY_STATUS_CODE_ERROR } from 'src/policy/enums/policy.status-code.enum';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { ENUM_AUTH_STATUS_CODE_ERROR } from 'src/auth/enums/auth.status-code.enum';
import { PERMISSIONS_META_KEY } from 'src/policy/constants/policy.constant';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { IUserDocument } from 'src/users/interfaces/user.interface';

@Injectable()
export class PolicyAbilityGuard implements CanActivate {
    constructor(
        private readonly reflector: Reflector,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const requiredPermissions = this.reflector.get<string[]>(PERMISSIONS_META_KEY, context.getHandler()) || [];
        
        const { __user, user } = context
            .switchToHttp()
            .getRequest<IRequestApp>() as { __user: IUserDocument, user: any };

        // const { __delegateUser } = context
        //     .switchToHttp()
        //     .getRequest<IRequestApp>() as { __delegateUser: IUserDocument };

        if (!__user && !user) {
            throw new ForbiddenException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.accessTokenUnauthorized',
            });
        }

        const  { type } = user || __user.role;
        // const delegateType = __delegateUser?.role?.type;

        // Super admin bypass all policy checks
        if (type === ENUM_ROLE_TYPE.SUPER_ADMIN || type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return true;
        } 
        
        // If no policies are required for this route
        if (requiredPermissions.length === 0) {
            throw new ForbiddenException({
                statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_PREDEFINED_NOT_FOUND,
                message: 'policy.error.abilityPredefinedNotFound',
            });
        }

        const permissionsSet = new Set();

        // [...__delegateUser?.role?.policies || [], ...__delegateUser?.assignedPolicies || []]
        //     .filter(policy => ![...__delegateUser?.restrictedPolicies || []].some(restrictedPolicy => 
        //         restrictedPolicy._id.toString() === policy._id.toString()
        //     ))
        //     .flatMap(policy => policy.permissions || [])
        //     .map(permission => {
        //         if (!permission.isActive) return;
        //         permissionsSet.add(permission.type)
        //     });

        [...__user.role?.policies || [], ...__user?.assignedPolicies || []]
            .filter(policy => ![...__user.restrictedPolicies || []].some(restrictedPolicy => 
                restrictedPolicy._id.toString() === policy._id.toString()
            ))
            .flatMap(policy => policy.permissions || [])
            .map(permission => {
                if (!permission.isActive) return;
                permissionsSet.add(permission.type)
            });

        const hasRequiredPermissions = requiredPermissions.every(
            (requiredPolicy: ENUM_PERMISSION_TYPE) => permissionsSet.has(requiredPolicy)
        );

        if (!hasRequiredPermissions) {
            throw new ForbiddenException({
                statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_FORBIDDEN,
                message: 'policy.error.abilityForbidden',
            });
        }

        return true;
    }
}
@Injectable()
export class PolicyAbilityDelegateGuard implements CanActivate {
    constructor(
        private readonly reflector: Reflector,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const requiredPermissions = this.reflector.get<string[]>(PERMISSIONS_META_KEY, context.getHandler()) || [];
        
        let { __user, user } = context
            .switchToHttp()
            .getRequest<IRequestApp>() as { __user: IUserDocument, user: any };

        if (!__user || !user) {
            throw new ForbiddenException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.accessTokenUnauthorized',
            });
        }

        // Super admin bypass all policy checks
        if (__user?.role?.type === ENUM_ROLE_TYPE.SUPER_ADMIN || __user?.role?.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return true;
        } 
        

        const { __delegateUser } = context
            .switchToHttp()
            .getRequest<IRequestApp>() as { __delegateUser: IUserDocument };

        if (!__delegateUser) {
            throw new ForbiddenException({
                statusCode: ENUM_AUTH_STATUS_CODE_ERROR.JWT_ACCESS_TOKEN,
                message: 'auth.error.sessionExpired',
            });
        }

        const  { type } = __delegateUser.role;
        // const delegateType = __delegateUser?.role?.type;

        // Super admin bypass all policy checks
        if (type === ENUM_ROLE_TYPE.SUPER_ADMIN || type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return true;
        } 
        
        // If no policies are required for this route
        if (requiredPermissions.length === 0) {
            throw new ForbiddenException({
                statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_PREDEFINED_NOT_FOUND,
                message: 'policy.error.abilityPredefinedNotFound',
            });
        }

        const permissionsSet = new Set();

        // [...__delegateUser?.role?.policies || [], ...__delegateUser?.assignedPolicies || []]
        //     .filter(policy => ![...__delegateUser?.restrictedPolicies || []].some(restrictedPolicy => 
        //         restrictedPolicy._id.toString() === policy._id.toString()
        //     ))
        //     .flatMap(policy => policy.permissions || [])
        //     .map(permission => {
        //         if (!permission.isActive) return;
        //         permissionsSet.add(permission.type)
        //     });

        [...__delegateUser.role?.policies || [], ...__delegateUser?.assignedPolicies || []]
            .filter(policy => ![...__delegateUser.restrictedPolicies || []].some(restrictedPolicy => 
                restrictedPolicy._id.toString() === policy._id.toString()
            ))
            .flatMap(policy => policy.permissions || [])
            .map(permission => {
                if (!permission.isActive) return;
                permissionsSet.add(permission.type)
            });

        const hasRequiredPermissions = requiredPermissions.every(
            (requiredPolicy: ENUM_PERMISSION_TYPE) => permissionsSet.has(requiredPolicy)
        );

        if (!hasRequiredPermissions) {
            throw new ForbiddenException({
                statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_FORBIDDEN,
                message: 'policy.error.abilityForbidden',
            });
        }

        return true;
    }
}
