
import { Body, Controller, Get, NotFoundException, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PolicyService } from 'src/policy/services/policy.service';
import { PolicyCreateRequestDto } from 'src/policy/dtos/request/policy.create.request.dto';
import { PaginationDto } from 'src/utils/dto/pagination.dto';
import { PaginationService } from 'src/common/pagination/services/pagination.service';
import { Response, ResponsePaging } from 'src/common/response/decorators/response.decorator';
import { MongoIdPipe } from 'src/common/database/pipes/mongo-id.pipe';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { PolicyAssignPermissionRequestDto } from 'src/policy/dtos/request/policy.assign-permission.request.dto';
import { PolicySuperAdminProtect } from '../decorators/policy.decorator';
import { AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';

@ApiTags('modules.admin.policy')
@Controller({
    version: '1',
    path: '/admin/policy',
})
@ApiBearerAuth()
export class PolicyAdminController {
    constructor(
        private readonly policyService: PolicyService,
        private readonly paginationService: PaginationService
    ) {}

    @Post('/create')
    @ApiOperation({ summary: 'Create a new policy' })
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    async create(
        @Body() policyCreateRequestDto: PolicyCreateRequestDto
    ): Promise<any> {
        const policy = await this.policyService.create(policyCreateRequestDto);
        return policy;
    }

    @ApiOperation({ summary: 'Get all policies' })
    @ResponsePaging("policy.setting-list")
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Post('/list')
    async findAll(
        @Body() { search, pageSize: limit, page }: PaginationDto
    ): Promise<any> {
        const _limit = limit || 10;
        const _page = page || 1;
        const _offset = this.paginationService.offset(_page, _limit);

        const {data: policies, total} = await this.policyService.getAllForSettingPolicies(
            {
                ...(search ? {
                    $or: [
                        { subject: { $regex: search, $options: 'i' } },
                        { module: { $regex: search, $options: 'i' } }
                    ]
                } : {}),
            },
            {
                paging: {
                    limit: _limit,
                    offset: _offset,
                },
            }
        );
        const totalPage: number = this.paginationService.totalPage(
            total,
            _limit
        );
        return {
            __pagination: {
                total: total,
                totalPage: totalPage,
                page: _page,
                limit: _limit,
            },
            data: policies
        };
    }

    @ApiOperation({ summary: 'List all available permissions' })
    @ResponsePaging('policy.listPermissions')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Get('/permission/list')
    async listPermissions(
        @Query() { search, pageSize: limit, page }: PaginationDto
    ) {
        const _limit = limit;
        const _offset = this.paginationService.offset(page, _limit);
        const _search = this.paginationService.search(search, ['name']);

        const { data, total } = await this.policyService.listPermissions(
            {
                isActive: true,
                ..._search,
            },
            {
                paging: {
                    limit: _limit,
                    offset: _offset,
                },
            }
        );
        const totalPage: number = this.paginationService.totalPage(
            total,
            _limit
        );
        return {
            _pagination: { 
                total,
                totalPage,
                page,
                limit: _limit
            },
            data: data
        };


    }

    @ApiOperation({ summary: 'List all available actions' })
    @Response('policy.list.actions')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Get('list/actions')
    async listActions() {
        return this.policyService.listActions();
    }

    @ApiOperation({ summary: 'Change policy status' })
    @Response('policy.status')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Post('/:id/status')
    async changeStatus(
        @Param('id', MongoIdPipe) id: IDatabaseObjectId
    ) {
        await this.policyService.changeStatus(id);
        return true;
    }

    @ApiOperation({ summary: 'Get a policy by ID' })
    @Response('policy.get')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Get('/:id/get')
    async findOneById(@Param('id', MongoIdPipe) id: IDatabaseObjectId): Promise<any> {
        const policy = await this.policyService.findOneById(id);

        if (!policy) {
            throw new NotFoundException('Policy not found');
        }

        return {
            data: policy
        };
    }

    @ApiOperation({ summary: 'Assign permissions to a policy' })
    @Response('policy.assign.permissions')
    @PolicySuperAdminProtect()
    @AuthJwtAccessProtected()
    @Put('/:id/assign-permissions')
    async assignPermissions(
        @Param('id', MongoIdPipe) id: IDatabaseObjectId,
        @Body() { permissions }: PolicyAssignPermissionRequestDto
    ): Promise<any> {
        const policy = await this.policyService.findOneById(id);
        
        if (!policy) {
            throw new NotFoundException('Policy not found');
        }

        await this.policyService.assignPermissions(id, permissions);
        
        return {
            data: { _id: id }
        };
    }
}
