import { SetMetadata, UseGuards, applyDecorators } from '@nestjs/common';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { PolicyAbilityDelegateGuard, PolicyAbilityGuard } from 'src/policy/guards/policy.ability.guard';
import { PolicyRoleGuard } from 'src/policy/guards/policy.role.guard';
import { PERMISSIONS_META_KEY, ROLE_META_KEY } from '../constants/policy.constant';
import { ENUM_PERMISSION_TYPE } from '../enums/policy.permissions.enum';
import { UseDelegatedUserGuard } from '../guards/policy.delegateUser.guard';



export function PolicyAbilityProtected(
    ...handlers: ENUM_PERMISSION_TYPE[]
): MethodDecorator {
    return applyDecorators(
        UseGuards(PolicyAbilityGuard),
        SetMetadata(PERMISSIONS_META_KEY, handlers)
    );
}

export function PolicyAbilityDelegateProtected(
    ...handlers: ENUM_PERMISSION_TYPE[]
): MethodDecorator {
    return applyDecorators(
        UseGuards(PolicyAbilityDelegateGuard),
        SetMetadata(PERMISSIONS_META_KEY, handlers)
    );
}

export function PolicyRoleProtected(
    ...roles: ENUM_ROLE_TYPE[]
): MethodDecorator {
    return applyDecorators(
        UseGuards(PolicyRoleGuard),
        SetMetadata(ROLE_META_KEY, roles)
    );
}

export function PolicySuperAdminProtect(
): MethodDecorator {
    return applyDecorators(
        UseGuards(PolicyRoleGuard),
    );
}
