import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { Category, CategoryDocument, CategoryLevel } from "src/merchandise/schema/category.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { CreateCategoryDto } from "../dto/createCategory.dto";
import { FilterCategoryDto } from "../dto/filterCategory.dto";
import { Product, ProductSchema, ProductType } from "src/merchandise/schema/product.schema";
import { RoleType } from "src/utils/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";

@Injectable()
export class CategoryService {
  constructor(
    @InjectModel(Category.name) private categoryModel: Model<CategoryDocument>,
    @InjectModel(Product.name) private productModel: Model<Product>,
    @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
    private readonly transactionService: TransactionService,
  ) { }

  async getOrganizationId(user: IUserDocument) {
    const roleType = user.role.type;
    if (!user._id) {
      throw new BadRequestException("User not found");
    }
    if (!user.role) {
      throw new BadRequestException("User not found");
    }
    if (roleType === ENUM_ROLE_TYPE.WEB_MASTER || roleType === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || roleType === ENUM_ROLE_TYPE.TRAINER) {
      const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
      if (!staffDetails) throw new BadRequestException("Staff not found");
      return staffDetails.organizationId;
    }
    if (roleType === ENUM_ROLE_TYPE.ORGANIZATION) {
      return user._id;
    }
    return user._id;
  }

  async categoryListing(filterCategoryDto: FilterCategoryDto, user: any): Promise<any> {

    const pageSize = filterCategoryDto?.pageSize ?? 10;
    const page = filterCategoryDto?.page ?? 1;
    const skip = pageSize * (page - 1);
    let query = {};
    if (filterCategoryDto.search) {
      const titleQueryString = filterCategoryDto.search.trim().split(" ").join("|");
      query["$or"] = [{ name: { $regex: `.*${titleQueryString}.*`, $options: "i" } }, { slug: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
    }

    if (filterCategoryDto?.status) {
      query["status"] = filterCategoryDto.status;
    }

    if (filterCategoryDto?.parentId) {
      query["parentId"] = filterCategoryDto.parentId;
    }

    if (filterCategoryDto?.level) {
      query["level"] = filterCategoryDto.level;
    }
    query["organizationId"] = await this.getOrganizationId(user);
    let dataProm;
    if (filterCategoryDto.page !== undefined && filterCategoryDto.pageSize !== undefined) {
      let countProm = this.categoryModel.countDocuments(query);
      dataProm = this.categoryModel
        .find(query, "_id name parentId slug level")
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(pageSize)
        .populate({
          path: "parentId",
          select: "name _id parentId", // Only fetch the 'name' field
        })
        .exec();

      let [count, list] = await Promise.all([countProm, dataProm]);
      return { count, list };
    } else {
      dataProm = await this.categoryModel
        .find(query, "_id name parentId slug  level")
        .populate({
          path: "parentId",
          select: "name _id parentId", // Only fetch the 'name' field
        })
        .exec();
      return { list: dataProm };
    }
  }

  async createCategory(createCategoryDto: CreateCategoryDto, user: any): Promise<CategoryDocument> {
    const session = await this.transactionService.startTransaction();

    try {
      const { name, parentId, level } = createCategoryDto;
      createCategoryDto["organizationId"] = await this.getOrganizationId(user);

      const existingCategory = await this.categoryModel.findOne({ name, organizationId: await this.getOrganizationId(user) });
      if (existingCategory) {
        throw new BadRequestException("Category Name already exists");
      }

      let parentCategory: CategoryDocument | null = null;

      if (parentId) {
        parentCategory = await this.categoryModel.findById(parentId);
        if (!parentCategory) {
          throw new NotFoundException("Parent category not found");
        }

        if (level === CategoryLevel.SECOND && parentCategory.level !== CategoryLevel.FIRST) {
          throw new BadRequestException("Invalid parent category level");
        }
      }

      const newCategory = new this.categoryModel(createCategoryDto);
      if (parentCategory) {
        parentCategory.children.push(newCategory._id);
        await parentCategory.save({ session });
      }

      const createdCategory = await newCategory.save({ session });

      await this.transactionService.commitTransaction(session);
      return createdCategory;

    } catch (error) {
      await this.transactionService.abortTransaction(session);
      throw error;
    } finally {
      session.endSession();
    }
  }
  async deleteCategory(id: string): Promise<any> {
    const category = await this.categoryModel.findById(id);
    if (category && category.children.length > 0) {
      throw new NotFoundException("Cannot delete a category that has children");
    }
    const isProductLinked = await this.productModel.findOne({
      $or: [{ firstCategoryId: id }, { secondaryCategoryId: id }]
    });
    if (isProductLinked) {
      throw new NotFoundException("Cannot delete a category it is linked with product");
    }
    if (category && category.parentId) {
      const parentCategory = await this.categoryModel.findById(category.parentId);
      if (parentCategory) {
        const index = parentCategory.children.indexOf(id);
        if (index > -1) {
          parentCategory.children.splice(index, 1);
          await parentCategory.save();
        }
      }
    }

    return this.categoryModel.findByIdAndDelete(id);
  }
  async getCategoryDetails(id: string): Promise<any> {
    if (!id) {
      throw new NotFoundException("Category Not found")
    }
    return await this.categoryModel.findById(id).populate({
      path: "parentId",
      select: "_id parentId level name",
      populate: {
        path: "parentId",
        select: "_id parentId level name",
      },
    });
  }

  async updateCategory(id: string, createCategoryDto: CreateCategoryDto,): Promise<any> {
    const category = await this.categoryModel.findOne({ _id: id, level: createCategoryDto.level });
    if (!category) throw new BadRequestException("Category not found");


    if (category.parentId !== createCategoryDto.parentId) {
      const checkParent = await this.categoryModel.findOne({ _id: createCategoryDto.parentId });
      if (!checkParent) throw new NotFoundException("Parent category not found");
      if (
        (createCategoryDto.level === CategoryLevel.SECOND && checkParent.level !== CategoryLevel.FIRST)
      ) {
        throw new NotFoundException("Invalid parent category level");
      }
      let updateCategory = await this.categoryModel.findOneAndUpdate({ _id: category["parentId"] }, { $pull: { children: id } });
      let updateNewParent = await this.categoryModel.findOneAndUpdate({ _id: createCategoryDto.parentId }, { $addToSet: { children: id } });
    }
    return this.categoryModel.findByIdAndUpdate(id, createCategoryDto);
  }

  async SecondarycategoryListing(filterCategoryDto: FilterCategoryDto, user: any) {
    let query = {};
    if (filterCategoryDto?.parentId) {
      query["parentId"] = filterCategoryDto.parentId;
    }

    if (filterCategoryDto?.level) {
      query["level"] = "second";
    }
    query["organizationId"] = await this.getOrganizationId(user);
    const subCategoryList = await this.categoryModel
      .find(query, "_id name parentId slug level")
      .sort({ createdAt: -1 })
      .exec();
    return subCategoryList
  }
}
