import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsDate, IsEnum, IsInt, IsMongoId, IsNumber, IsOptional, IsPositive, IsString, Max, Min, MinDate, ValidateIf } from "class-validator";
import { ProductType } from "../schema/product.schema";
import { Type } from "class-transformer";

export class CreateInventoryDto {

  @ApiProperty({ enum: ProductType, description: "Type of the product" })
  @IsString()
  @IsEnum(ProductType)
  productType: ProductType;

  @ApiProperty({ description: "MongoDB ID of the product" })
  @IsMongoId()
  productId: string;

  @ApiProperty({ description: "MongoDB ID of the product variant (if applicable)", required: false })
  @ValidateIf(o => o.productType === ProductType.VARIABLE)
  @IsMongoId()
  productVariantId: string;

  @ApiProperty({ description: "MongoDB ID of the store" })
  @IsMongoId()
  storeId: string;

  @ApiProperty({ description: "Sale price of the product", example: 499.99 })
  @IsNumber()
  @IsPositive()
  salePrice: number;

  @ApiProperty({ description: "Maximum retail price of the product", example: 599.99 })
  @IsNumber()
  @IsPositive()
  mrp: number;

  @ApiProperty({ description: "Available stock quantity", example: 10 })
  @IsInt()
  @Min(0, { message: "Quantity cannot be less than 0" })
  quantity: number;

  @ApiProperty({ description: "Discount percentage (0-100)", example: 10 })
  @IsNumber()
  @Max(100, { message: "Discount should be less than 100" })
  @Min(0, { message: "Discount should be greater than or equal to 0" })
  @IsOptional()
  @Type(() => Number) // Ensures correct type transformation
  discount: number;

  @ApiProperty({ description: "Final price after applying discount", example: 449.99 })
  @IsNumber()
  discountPrice: number;

  @ApiProperty({ description: "Expiry date of the product", example: "2025-12-31", required: false })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : null), { toClassOnly: true })
  @IsDate()
  @MinDate(new Date(), { message: 'Expiry date must be in the future' })
  expiryDate?: Date;
  
}
