import { BadRequestException, Body, Controller, Post, UseGuards, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { AttributeService } from "../services/attribute.service";
import { AttributeList } from "../constant/attributeList.constant";
import { CreateAttributeValueDto } from "../dto/createAttributeValue.dto";
import { FilterAttributeValueDto } from "../dto/filterAttributeValue.tro";

@ApiTags("attribute")
@ApiBearerAuth()
@Controller("attribute")
export class AttributeController {
    constructor(private attributeService: AttributeService) { }
    @Get("/") 
    @ApiOperation({ summary: "list of Attribute" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async index(): Promise<{ message: string; data: typeof AttributeList }> {
        return {
            message: "Attribute List",
            data: AttributeList,
        };
    }

    @Post("/create")
    @ApiOperation({ summary: "Create a new Attribute" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async createAttributeValue(
        @Body() createAttributeValueDto: CreateAttributeValueDto, @GetUser() user: any,) {
        try {
            const payload: any = {
                ...createAttributeValueDto,
                createdBy: user._id,
            };
            const result = await this.attributeService.createAttributeValue(payload, user);
            return {
                message: "Attribute Value created successfully",
                data: result
            };
        } catch (error) {
            console.log(error)
            throw new BadRequestException(error.message || "Attribute creation failed");
        }
    }


    @Post("/")
    @ApiOperation({ summary: "list of Attribute" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async attributeList(@Body() filterAttributeValueDto: FilterAttributeValueDto, @GetUser() user: any) {
        try {
            const attributeList = await this.attributeService.getAttributeValues(filterAttributeValueDto, user);
            return {
                message: "Attribute List Fetch Successfully",
                data: attributeList
            }
        } catch (error: any) {
            throw new BadRequestException(error.message || "Could not Fetch Attribute List")
        }
    }

    @Get("/:id")
    @ApiOperation({ summary: "Detail of Attribute" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async attributeValueDetails(@Param("id") id: string): Promise<{ message: string; data: any }> {
        return {
            message: "Attribute Value Details",
            data: await this.attributeService.getAttributeValueDetails(id),
        };
    }
    @Patch("/:id")
    @ApiOperation({ summary: "Update Detail of Attribute" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async updateAttributeValue(
        @Param("id") id: string,
        @Body() createAttributeValueDto: CreateAttributeValueDto): Promise<{ message: string; data: any }> {
        return {
            message: "Attribute Value updated successfully",
            data: await this.attributeService.updateAttributeValue(id, createAttributeValueDto),
        };
    }
    @Get("/brand/brand-list")
    @ApiOperation({ summary: "List of all the Brand" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async getBrandData(@GetUser() user: any) {
        const response = await this.attributeService.getBrandDetails(user)
        return {
            message: "Brand List Fetch successfully",
            data: response
        }
    }
    @Delete("/:id")
    @ApiOperation({ summary: "Delete the attribute" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION)
    async deleteAttributeValue(@Param("id") id: string): Promise<{ message: string }> {
        await this.attributeService.deleteAttributeValue(id);
        return {
            message: "Attribute Value deleted successfully",
        };
    }
}