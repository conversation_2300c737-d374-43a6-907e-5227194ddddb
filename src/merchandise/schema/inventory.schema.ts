import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { ProductType } from "./product.schema";

export type InventoryDocument = HydratedDocument<Inventory>;

@Schema({ timestamps: true })
export class Inventory {

    @Prop({ type: String, required: true, enum: ProductType })
    productType: ProductType;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "facility", index: true })
    storeId: String;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Product" })
    productId: String;

    @Prop({ type: SchemaTypes.ObjectId, ref: "ProductVariant" })
    productVariantId: String;

    @Prop({ type: Number, required: true, default: 0 })
    salePrice: number;

    @Prop({ type: Number, required: true })
    mrp: number;

    @Prop({ type: Number, required: true })
    quantity: number;

    @Prop({ type: Date, required: false, default: null })
    expiryDate: Date;

    @Prop({ type: Number, required: false, default: 0 })
    discount: number;

    @Prop({ type: Number, required: true, default: 0 })
    discountPrice: number;

    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    createdBy: string;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    organizationId: string;
}

export const InventorySchema = SchemaFactory.createForClass(Inventory);

