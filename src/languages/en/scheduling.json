{"create": {"session": "Session booked successfully", "personalAppointment": "Personal appointment booked successfully", "class": "Class booked successfully", "course": "Course booked successfully"}, "update": {"session": "Session updated successfully", "personalAppointment": "Personal appointment updated successfully", "class": "Class updated successfully", "course": "Course updated successfully"}, "delete": "Scheduling deleted successfully", "list": "Scheduling retrieved successfully", "get": "Scheduling details fetched successfully", "checkin": "Scheduling checkin successfully", "cancel": "Scheduling cancel successfully", "error": {"notFound": "Sorry, we couldn't find the requested scheduling.", "alreadyExists": "A scheduling with this name already exists.", "invalidDateRange": "Invalid date range for the scheduling.", "invalidTimeWindow": "Invalid time window for the scheduling."}}