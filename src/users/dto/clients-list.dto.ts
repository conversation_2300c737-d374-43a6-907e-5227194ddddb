import { ApiProperty } from "@nestjs/swagger";
import { PaginationDTO } from "./pagination.dto";
import { IsMongoId, IsNotEmpty, IsOptional, IsString,IsBoolean } from "class-validator";

export class ClientsListDto extends PaginationDTO {
    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Knox",
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    search: string;

    @ApiProperty({
        description: "Id of the associated facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid type of facility" })
    facilityId: string;

    @ApiProperty({
        description: "Id of the not to be included  client.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid type of clientId" })
    notIncludedClientId: string;

    @ApiProperty({
        description: "Id of the associated Location.",
        example: ["659d268dee4b6081dacd41fd"],
        required: false,
        type: Array<String>,
    })
    @IsOptional()
    locationId?: Array<string>;

    @ApiProperty({
        description: "Active client list",
        type: Boolean,
        example: false,
    })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}
