import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsDateString, IsArray, ValidateNested, IsMongoId, IsEnum } from "class-validator";
import { Types } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PaginationDTO } from "./pagination.dto";

export class PricingListDTO extends PaginationDTO{

    @ApiProperty({
        description: "Start date",
        example: Date.now(),
        required: false,
    })
    @IsOptional()
    startDate?: Date;

    @ApiProperty({
        description: "End date",
        example: Date.now(),
        required: false,
    })
    @IsOptional()
    endDate?: Date;

    @ApiProperty({
        description: "The IDs of the Branchesfor which packages are to be fetched",
        example: ["66ceffc7a13db5380edb06b1"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "FacilityId must be an array" })
    facilityIds?: Types.ObjectId[];

    @ApiProperty({
        description: "The IDs of the Service Category",
        example: ["677d178a0972f558ecd9b84b", "677cb367ae4a154339d8d487"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Service Category Ids must be an array" })
    serviceCategoryIds?: Types.ObjectId[];
}

