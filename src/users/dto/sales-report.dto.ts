import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsArray, ArrayNotEmpty, IsOptional } from "class-validator";
import { Types } from "mongoose";

export class ExportSalesReportDto {
    @ApiProperty({
        description: "Start date of report range",
        example: "2024-09-15T00:00:00Z",
    })
    @IsNotEmpty({ message: "Start date is required and cannot be empty" })
    startDate: Date;

    @ApiProperty({
        description: "End date of report range",
        example: "2024-09-20T00:00:00Z",
    })
    @IsNotEmpty({ message: "End date is required and cannot be empty" })
    endDate: Date;

    @ApiProperty({
        description: "Facility IDs for which sales report is to be generated",
        example: ["66ceffc7a13db5380edb06b1"],
    })
    @IsNotEmpty({ message: "FacilityIds are required" })
    @IsArray({ message: "FacilityIds must be an array" })
    @IsOptional()
    @ArrayNotEmpty({ message: "FacilityIds array must not be empty" })
    facilityIds?: Types.ObjectId[];

    @ApiProperty({
        description: "Response type for export",
        enum: ['string', 'stream'],
        default: 'stream'
    })
    @IsNotEmpty({ message: "Response type is required" })
    @IsEnum(['string', 'stream'], {
        message: "Response type must be either 'string' or 'stream'",
    })
    responseType: 'string' | 'stream';

}