import { ApiProperty } from '@nestjs/swagger';

export class PurchaseResponseDto {
  @ApiProperty({
    description: 'Whether the purchase was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message about the purchase',
    example: 'Purchase completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Invoice ID',
    example: '60d21b4667d0d8992e610c85',
    required: false,
  })
  invoiceId?: string;

  @ApiProperty({
    description: 'Invoice number',
    example: 1001,
    required: false,
  })
  invoiceNumber?: number;

  @ApiProperty({
    description: 'Order ID',
    example: 5001,
    required: false,
  })
  orderId?: number;

  @ApiProperty({
    description: 'Grand total of the purchase',
    example: 165.17,
    required: false,
  })
  grandTotal?: number;

  @ApiProperty({
    description: 'Amount paid for the purchase',
    example: 165.17,
    required: false,
  })
  amountPaid?: number;

  @ApiProperty({
    description: 'Error details if the purchase failed',
    example: 'Insufficient stock for product X',
    required: false,
  })
  error?: string;
}
