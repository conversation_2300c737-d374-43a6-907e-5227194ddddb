import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsDate, IsNotEmpty, IsOptional, IsString, Validate } from "class-validator";


export class UpdateSuspensionDto {
    @ApiProperty({ 
        description: 'The ID of the purchase',
        example: '66b5b5b5b5b5b5b5b5b5b5b5'
    })
    @IsString()
    @IsNotEmpty()
    purchaseId: string;

    @ApiProperty({ 
        description: 'The ID of the suspension',
        example: '66b5b5b5b5b5b5b5b5b5b5b5'
    })
    @IsString()
    @IsNotEmpty()
    suspensionId: string;

    @ApiProperty({
        description: "From Date",
        type: Date,
        example: new Date(new Date().setDate(new Date().getDate() + 2)),
    })
    @IsNotEmpty()
    @IsDate()
    @Type(() => Date)
    @Validate((value: Date) => {
        if (value <= new Date()) {
            return false;
        }
        return true;
    }, {
        message: 'From date must be greater than current time'
    })
    fromDate: Date;

    @ApiProperty({
        description: "To Date",
        type: Date,
        example: new Date(new Date().setDate(new Date().getDate() + 12)),
    })
    @IsNotEmpty()
    @IsDate()
    @Type(() => Date)
    endDate: Date;

    @ApiProperty({ 
        description: 'The notes of the suspension',
        example: 'This is a note'
    })
    @IsString()
    @IsOptional()
    notes?: string;
}