import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsMongoId, IsOptional, IsString, Length, ValidateNested } from "class-validator";

class PoliciesDto {
    @ApiProperty({
        description: "Type of Policy",
        example: "Facility Waiver",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "Policy must be between 2 and 50 characters" })
    policyType: string;

    @ApiProperty({
        description: "Url of uploaded document",
        example: "https://document.com",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Required valid document url" })
    documentUrl: string;

    @ApiProperty({
        description: "Policy is enabled or not || Boolean",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "Invalid type of Policy" })
    isEnabled: boolean;
}

export class UpdatePoliciesDto {
    @ApiProperty({
        description: "Id of the associated facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid type of facility" })
    facilityId: string;

    @ApiProperty({
        description: "The Policies.",
        example: PoliciesDto,
        required: true,
    })
    @ValidateNested({ message: "Policies are invalid" })
    @Type(() => PoliciesDto)
    policies: PoliciesDto[];
}
