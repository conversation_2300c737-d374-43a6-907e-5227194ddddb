import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsEmail, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, Length, Max, <PERSON>ength, Min, Validate<PERSON>ested, ValidateIf, isNotEmpty } from "class-validator";
import { ActivityLevel } from "src/utils/enums/activity-level.enum";
import { Gender } from "src/utils/enums/gender.enum";
import { AddressDto } from "./address.dto";
import { BusinessAddressDto } from "./business-address.dto";



class PoliciesDto {
    @ApiProperty({
        description: "Type of Policy",
        example: "Facility Waiver",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "Policy must be between 2 and 50 characters" })
    policyType: string;

    @ApiProperty({
        description: "Url of uploaded document",
        example: "https://document.com",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Required valid document url" })
    documentUrl: string;

    @ApiProperty({
        description: "Policy is enabled or not || Boolean",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "Invalid type of Policy" })
    isEnabled: boolean;
}

export class ClientsDto {
    @ApiProperty({
        description: "Id of the associated facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid type of facility" })
    facilityId: string;

    @ApiProperty({
        description: "First name of the client.",
        example: "LND",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "First Name must be between 2 and 50 characters" })
    firstName: string;

    @ApiProperty({
        description: "Last name of the client.",
        example: "Abhay",
        // minLength: 2,
        maxLength: 50,
        required: false,
    })
    @IsOptional()
    // @Length(2, 50, { message: "Last Name must be between 2 and 50 characters" })
    lastName?: string;

    @ApiProperty({
        description: "DOB of the client.",
        example: "Date of the birth in ISO format",
        required: true,
    })
    @IsDate({ message: "Required valid DOB" })
    @Type(() => Date)
    @IsOptional()
    dob?: Date;

    @ApiProperty({ description: "Gender of the client", enum: Gender, example: Gender.MALE, required: false })
    @IsOptional()
    @IsEnum(Gender, { message: "Invalid gender value" })
    gender: Gender;

    @ApiProperty({ description: "Activity level of the client", enum: ActivityLevel, example: ActivityLevel.ADVANCE, required: false })
    @IsOptional()
    activityLevel?: ActivityLevel;

    @ApiProperty({
        description: "The mobile number of the client.",
        example: "9876543212",
        //minLength: 10,
        //maxLength: 10,
        required: false,
    })
    @ValidateIf((o) => !o.email)
    @IsNotEmpty({ message: "Either Mobile Number or Email is required" })
    //@Length(10, 10, { message: "Mobile No. must be exactly 10 digits" })
    @IsString()
    mobile: string;

    @ApiProperty({
        description: "country code for the Mobile",
        example: "+91",
        required: false
    })
    @ValidateIf((o) => o.mobile)
    @IsNotEmpty({ message: "Country Code is Required" })
    countryCode: string

    @ApiProperty({
        description: "The email address for the Organization.",
        example: "<EMAIL>",
        maxLength: 255,
        required: false,
    })
    @Transform(({ value }) => value.toLowerCase())
    @ValidateIf((o) => !o.mobile) // Validate only if mobile is not provided
    @IsNotEmpty({ message: "Either Mobile Number or Email is required" })
    @IsEmail({}, { message: "Invalid Email Format" })
    @MaxLength(255, { message: "Email must be less than 255 characters" })
    email?: string;

    @ApiProperty({
        description: "The address of the client.",
        example: AddressDto,
        required: false,
    })
    @IsOptional()
    @ValidateNested({ message: "Address is invalid" })
    @Type(() => AddressDto)
    @IsNotEmpty({ message: "Address is required" })
    address: AddressDto;

    @ApiProperty({
        description: "The business address of the client.",
        required: false,
    })
    @ValidateIf((obj)=> obj.isBusiness)
    @IsOptional()
    @ValidateNested({ message: "Business address is invalid" })
    @Type(() => BusinessAddressDto)
    businessAddress?: BusinessAddressDto;

    @ApiProperty({
        description: "Mark as business",
        required: false,
    })
    @IsOptional()
    @Type(() => Boolean)
    isBusiness?: boolean = false;

    @ApiProperty({
        description: "Emergency Contact person name.",
        example: "Abhay Gupta",
        minLength: 2,
        maxLength: 50,
        required: false,
    })
    @IsOptional()
    @Length(2, 50, { message: "Required valid contacted person name" })
    emergencyContactPerson: string;

    @ApiProperty({
        description: "Emergency Contact person phone.",
        example: "9876543212",
        //minLength: 10,
        //maxLength: 10,
        required: false,
    })
    @IsOptional()

    //@Length(10, 10, { message: "Please enter valid Emergency contact number" })
    emergencyContactPhone: string;

    @ApiProperty({
        description: "The Policies.",
        example: PoliciesDto,
        required: true,
    })
    @ValidateNested({ message: "Policies are invalid" })
    @Type(() => PoliciesDto)
    policies: PoliciesDto[];

    @ApiProperty({
        description: "Photo of the client.",
        example: "https://dnfkjsdfdlf.png",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Image must be a valid string" })
    @IsOptional()
    photo?: string;
}
