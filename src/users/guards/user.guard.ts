import {
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Injectable,
} from '@nestjs/common';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { AuthService } from 'src/auth/services/auth.service';
import { UserService } from 'src/users/services/user.service';
import { IUserDocument } from 'src/users/interfaces/user.interface';
// import { ENUM_USER_STATUS } from 'src/users/enums/user.enum';
import { ENUM_USER_STATUS_CODE_ERROR } from 'src/users/enums/user.status-code.enum';
import { ENUM_ROLE_STATUS_CODE_ERROR } from 'src/role/enums/role.status-code.enum';
import { SessionService } from 'src/session/services/session.service';
import { CachingService } from 'src/common/caching/services/caching.service';

@Injectable()
export class UserGuard implements CanActivate {
    constructor(
        private readonly userService: UserService
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<IRequestApp>();
        const { user, organization } = request.user;
        const { __user: __user } = request;

        const userWithRole: IUserDocument = __user ||
            await this.userService.findOneWithRoleAndPermissions({
                _id: user,
            });

        if (!userWithRole.isActive) {
            throw new ForbiddenException({
                statusCode: ENUM_USER_STATUS_CODE_ERROR.INACTIVE_FORBIDDEN,
                message: 'user.error.inactive',
            });
        }
        if (!userWithRole.role.isActive) {
            throw new ForbiddenException({
                statusCode: ENUM_ROLE_STATUS_CODE_ERROR.INACTIVE_FORBIDDEN,
                message: 'role.error.inactive',
            });
        }

        // const checkPasswordExpired: boolean =
        //     await this.authService.checkPasswordExpired(
        //         userWithRole.passwordExpired
        //     );
        // if (checkPasswordExpired) {
        //     throw new ForbiddenException({
        //         statusCode: ENUM_USER_STATUS_CODE_ERROR.PASSWORD_EXPIRED,
        //         message: 'auth.error.passwordExpired',
        //     });
        // }

        request.__user = userWithRole;
        request.__organizationId = organization;

        return true;
    }
}
