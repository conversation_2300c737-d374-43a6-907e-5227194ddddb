import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus } from "@nestjs/common";
import { HttpArgumentsHost } from "@nestjs/common/interfaces";
import { ConfigService } from "@nestjs/config";
import { Response } from "express";
import { MongoError } from "mongodb";
import { ENUM_MESSAGE_LANGUAGE } from "src/common/message/enums/message.enum";
import { MessageService } from "src/common/message/services/message.service";
import { IRequestApp } from "src/common/request/interfaces/request.interface";

// https://stackoverflow.com/a/64682602

@Catch(Error)
export class MongoExceptionFilter implements ExceptionFilter {

    constructor(
        private readonly messageService: MessageService,
        private readonly configService: ConfigService,
    ) { }

    catch(exception: Error, host: ArgumentsHost) {
        const ctx: HttpArgumentsHost = host.switchToHttp();
        const response: Response = ctx.getResponse<Response>();
        const request: IRequestApp = ctx.getRequest<IRequestApp>();

        const xLanguage: string = request.__language ??
            this.configService.get<ENUM_MESSAGE_LANGUAGE>('message.language');

        let error: { statusCode: number; message: Array<string>, stackTrace?: string, error?: any };
        let status = HttpStatus.INTERNAL_SERVER_ERROR;
        let value = [];
        let errMessage = [];

        // Log the incoming exception to track its structure
        console.log("Caught exception:", exception);

        if (exception instanceof MongoError) {
            if (exception.code === 11000) {
                const keyValue = exception['keyValue'];
                const keyPattern = exception['keyPattern'];

                // Handle specific duplicate key cases
                if (keyPattern && keyPattern.organizationId && keyPattern.couponCode) {
                    status = HttpStatus.CONFLICT;
                    errMessage = [`Promotion code '${keyValue.couponCode}' already exists`];
                } else {
                    // Generic duplicate key error
                    const duplicateKey = Object.keys(keyValue)[0];
                    status = HttpStatus.CONFLICT;
                    errMessage = [`${duplicateKey} already exists`];
                }

                error = {
                    statusCode: status,
                    message: errMessage
                };
            } else {
                console.log("No keyValue found in MongoError.");
            }

            // If MongoError does not have a code, return an internal server error
            if (!exception.code) {
                console.error("MongoError with no code. Returning internal server error.");
                status = HttpStatus.INTERNAL_SERVER_ERROR;
                error = {
                    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                    message: ["Internal server error occurred in MongoDB operation."],
                };
                // return response.status(status).json(error);
            }

            // MongoDB Duplicate Key Error
            switch (exception.code) {
                case 11000: {
                    console.error(`MongoError 11000: Duplicate key error on ${value[0]}`);
                    errMessage[0] = errMessage[0] ?? "User with the same " + value[0] + " already exists";
                    status = HttpStatus.BAD_REQUEST;
                    error = {
                        statusCode: HttpStatus.BAD_REQUEST,
                        message: errMessage,
                    };
                    break;
                }
                default: {
                    console.error(`MongoError: Unknown code ${exception.code}. Returning internal server error.`);
                    errMessage[0] = "Internal Database error";
                    status = HttpStatus.INTERNAL_SERVER_ERROR;
                    error = {
                        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                        message: errMessage,
                    };
                    break;
                }
            }
        } else if(exception["message"] === 'request.validation'){
            // Use pre-formatted errors if available, otherwise format them here
            let formattedErrors = exception["formattedErrors"] || [];

            // If no formatted errors but we have raw errors, format them
            if (formattedErrors.length === 0 && exception["errors"]) {
                const validationErrors = exception["errors"];
                // Helper function to recursively process validation errors
                const processValidationErrors = (error: any, parentPath = '') => {
                    const currentPath = parentPath ? `${parentPath}.${error.property}` : error.property;

                    // Process constraints at current level
                    if (error.constraints) {
                        const constraints = Object.values(error.constraints);
                        formattedErrors.push(`${currentPath}: ${constraints.join(', ')}`);
                    }

                    // Process nested errors recursively
                    if (error.children && error.children.length > 0) {
                        for (const childError of error.children) {
                            processValidationErrors(childError, currentPath);
                        }
                    }
                };

                // Process all validation errors
                for (const error of validationErrors) {
                    processValidationErrors(error);
                }
            }

            status = HttpStatus.UNPROCESSABLE_ENTITY;
            error = {
                statusCode: status,
                message: formattedErrors.length > 0 ? formattedErrors : ["Validation failed. Please check your input and try again."],
            };
            // return response.status(status).json(exception);
        } else {
            // Log exception type and message for all other exceptions
            let exceptionName = exception["name"];
            let resStatusCode = (exception as any)?.response?.statusCode;
            console.log("Exception Name:", exceptionName);
            console.log("Exception Message:", exception["message"]);
            switch (exceptionName) {
                case "ValidationError":
                    console.error("ValidationError:", exception["_message"]);
                    errMessage[0] = exception["_message"] ?? "Something is missing";
                    status = HttpStatus.BAD_REQUEST;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.BAD_REQUEST,
                        message: errMessage,
                    };
                    break;
                case "UnprocessableEntityException":
                    console.error("UnprocessableEntityException:", exception["response"]);
                    status = HttpStatus.BAD_REQUEST;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.BAD_REQUEST,
                        message: exception["response"]?.["message"] ?? "Something went wrong",
                    };
                    break;
                case "NotFoundException":
                    console.error("NotFoundException:", exception["response"]);
                    errMessage[0] = exception["response"]?.["message"] ?? "Resource not found";
                    status = HttpStatus.NOT_FOUND;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.NOT_FOUND,
                        message: errMessage,
                    };
                    break;
                case "BadRequestException":
                    console.error("BadRequestException:", exception["response"]);
                    errMessage[0] = exception["response"]?.["message"] ?? "Bad request";
                    status = HttpStatus.BAD_REQUEST;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.BAD_REQUEST,
                        message: errMessage,
                    };
                    break;
                case "ForbiddenException":
                    console.error("ForbiddenException: Access denied");
                    errMessage[0] =  exception["response"]?.["message"] ?? "Access denied";
                    status = HttpStatus.FORBIDDEN;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.FORBIDDEN,
                        message: errMessage,
                    };
                    break;
                case "TypeError":
                    console.error("TypeError: Invalid values passed");
                    errMessage[0] = "Invalid values passed";
                    status = HttpStatus.BAD_REQUEST;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.BAD_REQUEST,
                        message: errMessage,
                    };
                    break;
                case "UnauthorizedException":
                    console.error("UnauthorizedException: Session expired");
                    errMessage[0] = exception["response"]?.["message"] ?? "Session expired. Please login again to continue";
                    status = HttpStatus.UNAUTHORIZED;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.UNAUTHORIZED,
                        message: errMessage,
                    };
                    break;
                case "Error":
                    console.error("General Error:", exception["message"]);
                    errMessage[0] = exception["message"] ?? "An unexpected error occurred";
                    status = HttpStatus.INTERNAL_SERVER_ERROR;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.INTERNAL_SERVER_ERROR,
                        message: errMessage,
                    };
                    break;
                case "PayloadTooLargeError":
                    console.error("PayloadTooLargeError:", exception["message"]);
                    errMessage[0] = exception["message"] + " / try uploading a smaller image";
                    status = HttpStatus.BAD_REQUEST;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.BAD_REQUEST,
                        message: errMessage,
                    };
                    break;
                case "ConflictException":
                    console.error("ConflictException:", exception["message"]);
                    errMessage[0] = exception["message"] ?? "Conflict occurred";
                    status = HttpStatus.CONFLICT;
                    error = {
                        statusCode: resStatusCode ?? HttpStatus.CONFLICT,
                        message: errMessage,
                    };
                    break;
                case "MongoServerError":
                    let exceptionCode = exception["code"];
                    console.error("MongoServerError with code:", exceptionCode);
                    if (exceptionCode === 11000) {
                        console.error("MongoServerError 11000: Duplicate key");
                        const keyPattern = exception["keyPattern"];
                        errMessage[0] = keyPattern ?
                            `${Object.keys(keyPattern)[0]} already exists` :
                            "Invalid details";
                        error = {
                            statusCode: HttpStatus.CONFLICT,
                            message: errMessage,
                        };
                    } else {
                        console.error("MongoServerError: Unknown error code", exceptionCode);
                        error = {
                            statusCode: HttpStatus.BAD_REQUEST,
                            message: ["Something went wrong with MongoDB."],
                        };
                    }
                    break;
                default: {
                    console.error("Unknown exception type:", exceptionName);
                    errMessage[0] = exception["message"] ?? "Internal server error";
                    status = HttpStatus.INTERNAL_SERVER_ERROR;
                    error = {
                        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                        message: errMessage,
                    };
                }
            }
        }

        if (process?.env?.NODE_ENV === 'development') {
            console.log("Exception stack trace:", exception.stack);
            error.stackTrace = exception.stack;
            error.error = exception.message
        }

        error.message = error.message.map((message) => {
            return this.messageService.setMessage(message, {
                customLanguage: xLanguage
            });
        });

        return response.status(status).json(error);
    }
}
