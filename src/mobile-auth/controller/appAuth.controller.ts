import { BadRequestException, Body, Controller, Get, HttpCode, HttpStatus, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/policy/enums/policy.enum";
import { OrganizationListDto } from "../dto/organization-list.dto";
import { MobileAuthService } from "../services/appAuth.service";
import { AppLoginDto } from "../dto/app-login.dto";
import { RequestOtpDto } from "../dto/app-request-otp.dto";
import { VerifyOtpDto } from "../dto/app-verifyOtp";
import { OrganizationsListDto } from "../dto/organizations-list.dto";
import { DeleteAccountDto } from "../dto/deleteAccoutn.dto";
import { SetPasswordDto } from "../dto/set-password.dto";
import { GetUser } from "src/auth/decorators/get-user.decorator";
@ApiTags("Mobile-Authentication")
@ApiBearerAuth()
@Controller("mobile-auth")
export class AppAuthController {
    constructor(private mobileAuthService: MobileAuthService) { }

    @Post("/organization/list")
    @ApiOperation({ summary: "Organizations listing for Mobile App" })
    async organizationList(@Body() organizationListDto: OrganizationListDto): Promise<any> {
        return await this.mobileAuthService.organizationList(organizationListDto);
    }

    @Post("/login")
    @HttpCode(200)
    @ApiOperation({ summary: "Login a user" })
    async login(@Body() loginDto: AppLoginDto) {
        const data = await this.mobileAuthService.login(loginDto);
        return {
            message: "User Login",
            data: data,
        };
    }
    @Post("/request-otp")
    @HttpCode(200)
    @ApiOperation({ summary: "Request OTP for authentication" })
    async requestOTP(@Body() requestOtpDto: RequestOtpDto) {
        const createOtp = await this.mobileAuthService.createOtp(requestOtpDto);
        return {
            message: "OTP generated",
            data: {
                otp: createOtp,
            },
        };
    }
    @Post("/verify-otp")
    @HttpCode(200)
    @ApiOperation({ summary: "Verify the OTP" })
    async verifyOTP(@Body() verifyOtpDto: VerifyOtpDto) {
        const verifyOtp = await this.mobileAuthService.verifyOtp(verifyOtpDto);
        return verifyOtp;
    }

    @Post("/forget-password-request-otp")
    @HttpCode(200)
    @ApiOperation({ summary: "Request OTP for password reset" })
    async forgetPasswordRequestOtp(@Body() requestOtpDto: RequestOtpDto) {
        const createOtp = await this.mobileAuthService.forgetPasswordOtp(requestOtpDto);
        return {
            message: "OTP generated",
            data: {
                otp: createOtp,
            },
        };
    }

    @Post("/organizations/list")
    @ApiOperation({ summary: "Organizations listing for Mobile App" })
    async organizationsList(@Body() organizationListDto: OrganizationsListDto): Promise<any> {
        return await this.mobileAuthService.organizationsList(organizationListDto);
    }
    @Post("/delete-account")
    @ApiOperation({ summary: "Delete account" })
    async deleteAccount(@Body() deleteAccountDto: DeleteAccountDto): Promise<any> {
        const response = this.mobileAuthService.deleteAccount(deleteAccountDto);
        return {
            message: "Your account Deletion request has been submitted successfully",
        }
    }
    @Post("/set-password")
    @ApiOperation({ summary: " Set password of the Trainer" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.TRAINER)
    async SetPasswordDto(@Body() setPasswordDto: SetPasswordDto, @GetUser() user: any,): Promise<any> {
        const response = await this.mobileAuthService.setTrainerPassword(setPasswordDto, user)
        return {
            message: "Password updated",
            data: "",
        };
    }
}
