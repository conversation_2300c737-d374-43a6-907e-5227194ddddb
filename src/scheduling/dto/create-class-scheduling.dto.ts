import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { <PERSON>Array, IsBoolean, IsDate, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, ValidateNested } from "class-validator";

export class TimeSlotsDTO {
    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsOptional()
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsOptional()
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;
}

export class ScheduleDTO {
    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Monday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    mon: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Tuesday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    tue: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Wednesday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    wed: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Thursday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    thu: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Friday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    fri: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Saturday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    sat: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Sunday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    sun: TimeSlotsDTO[];
}

export class CreateClassSchedulingDto {
    @ApiProperty({
        description: "The Id of the facility",
        example: "66cedf7a731d1269a4157a2d",
    })
    @IsMongoId({ message: "Invalid facility details" })
    facilityId: string;

    @ApiProperty({
        description: "Start date from when the scheduling will get created",
        example: "2024-10-01T00:00:00.000+00:00",
        required: true,
    })
    @IsDate({ message: "Required valid start date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Invalid start date" })
    fromDate: Date;

    @ApiProperty({
        description: "End date from when the scheduling can be done",
        example: "2024-10-01T00:00:00.000+00:00",
        required: true,
    })
    @IsDate({ message: "Required valid end date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Invalid start date" })
    endDate: Date;

    @ApiProperty({
        description: "The Id of the selected pay rate",
        example: "66cedf7a731d1269a4157a2d",
    })
    @IsMongoId({ message: "Invalid pay rate details" })
    payRateId: string;

    @ApiProperty({
        description: "The ID of the selected staff",
        example: "66cecb432351713ae4447a6b",
    })
    @IsMongoId({ message: "Invalid Staff details" })
    staffId: string;

    @ApiProperty({
        description: "The schedule for classes booking",
        type: ScheduleDTO,
    })
    @Type(() => ScheduleDTO)
    @IsNotEmpty({ message: "schedule  are required" })
    schedule: ScheduleDTO;

    @ApiProperty({
        description: "Total capacity of class",
        type: Number,
        example: 3,
    })
    @IsNotEmpty()
    @IsNumber()
    totalCapacity: number;

    @ApiProperty({
        description: "To check if pay later is available or not for this class",
        type: Boolean,
        example: false,
    })
    @IsBoolean()
    allowPayLater: Boolean;

    @ApiProperty({
        description: "To check if free class is available or not for this class",
        type: Boolean,
        example: false,
    })
    @IsBoolean()
    freeClassAvailable: Boolean;
}
