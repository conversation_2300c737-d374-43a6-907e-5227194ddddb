import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types, ObjectId, SchemaTypes, Document } from "mongoose";
import { DateRange } from "src/utils/enums/date-range-enum";
import { ScheduleStatusType } from "../enums/schedule-status.enum";
import { ClassType } from "src/utils/enums/class-type.enum";

@Schema({ timestamps: true })
export class Scheduling extends Document {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    scheduledBy: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Facility" })
    facilityId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User" })
    trainerId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({
        type: SchemaTypes.ObjectId,
        required: function (this: any) {
            return this.classType !== ClassType.COURSES;
        },
        ref: "User",
    })
    clientId: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Pricing" })
    packageId: ObjectId;

    @Prop({
        type: SchemaTypes.ObjectId,
        required: function (this: any) {
            return this.classType !== ClassType.COURSES;
        },
        ref: "Purchase",
    })
    purchaseId: ObjectId;

    @Prop({ type: String, required: true })
    classType: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Attributes" })
    subTypeId: ObjectId;

    // @Prop({ type: SchemaTypes.ObjectId, required: false,  ref: "Services", default: null })
    // payRateId: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Services" })
    serviceCategoryId: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "Room", default: null })
    roomId?: ObjectId;

    @Prop({
        type: Number,
        required: function (this: any) {
            return this.classType === ClassType.COURSES;
        },
    })
    classCapacity?: number;

    // @Prop({ type: Boolean, required: false, default: false })
    // sendConfirmation: boolean;

    @Prop({ type: String, enum: DateRange, required: false, default: DateRange.SINGLE })
    dateRange: string;

    @Prop({ type: Number, required: true })
    duration: number;

    @Prop({ type: Number, required: true })
    sessions: number;

    @Prop({ type: Date, required: true })
    date: Date;

    @Prop({
        type: String,
        required: false,
        match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
    })
    from?: string;

    @Prop({
        type: String,
        required: false,
        match: /^([0-1]\d|2[0-3]):([0-5]\d)$/,
    })
    to?: string;

    @Prop({
        type: String,
        enum: ScheduleStatusType,
        default: ScheduleStatusType.BOOKED,
        required: false,
    })
    scheduleStatus?: string;

    @Prop({ type: String, required: false })
    notes: string;

    @Prop({
        index: true,
        required: false,
        type: SchemaTypes.ObjectId,
        ref: "User",
        default: undefined,
    })
    canceledBy?: Types.ObjectId;

    @Prop({
        index: true,
        required: false,
        type: Date,
        default: undefined,
    })
    canceledAt?: Date;

    @Prop({
        index: true,
        required: false,
    })
    createdAt?: Date;

    @Prop({
        index: true,
        required: false,
    })
    updatedAt?: Date;
}

export const SchedulingSchema = SchemaFactory.createForClass(Scheduling);

SchedulingSchema.pre("save", function (next) {
    if (this.isModified("canceledBy")) {
        this.canceledAt = new Date();
    }
    next();
});
