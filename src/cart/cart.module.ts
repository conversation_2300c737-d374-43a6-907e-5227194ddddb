import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CartController } from './controllers/cart.controller';
import { CartService } from './services/cart.service';
import { Pricing, PricingSchema } from 'src/organization/schemas/pricing.schema';
import { Product, ProductSchema } from 'src/merchandise/schema/product.schema';
import { ProductVariant, ProductVariantSchema } from 'src/merchandise/schema/product-variant.schema';
import { Inventory, InventorySchema } from 'src/merchandise/schema/inventory.schema';
import { CustomPackage, CustomPackageSchema } from 'src/customPackage/schemas/custom-package.schema';
import { PromotionsModule } from 'src/promotions/promotions.module';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';
import { AuthModule } from 'src/auth/auth.module';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { Clients, ClientSchema } from 'src/users/schemas/clients.schema';
import { ActiveTimeFrameService } from 'src/utils/services/active-time-frame.service';

@Module({
  imports: [
    PromotionsModule,
    MongooseModule.forFeature([
      { name: Pricing.name, schema: PricingSchema },
      { name: Product.name, schema: ProductSchema },
      { name: ProductVariant.name, schema: ProductVariantSchema },
      { name: Inventory.name, schema: InventorySchema },
      { name: CustomPackage.name, schema: CustomPackageSchema },
      { name: User.name, schema: UserSchema },
      { name: Clients.name, schema: ClientSchema },
    ], DATABASE_PRIMARY_CONNECTION_NAME),
  ],
  controllers: [CartController],
  providers: [CartService, ActiveTimeFrameService],
  exports: [CartService],
})
export class CartModule {}
