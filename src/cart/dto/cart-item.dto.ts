import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, Min, ValidateIf } from 'class-validator';
import { DiscountDto } from 'src/organization/dto/create-pricing.dto';
import { ENUM_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';

export class CartItemDto {
  @ApiProperty({
    description: 'The type of item (product, service, custom package)',
    enum: ENUM_ITEM_TYPE,
    example: ENUM_ITEM_TYPE.PRODUCT,
  })
  @IsEnum(ENUM_ITEM_TYPE)
  @IsNotEmpty()
  itemType: ENUM_ITEM_TYPE;

  @ApiProperty({
    description: 'The ID of the item',
    example: '60d21b4667d0d8992e610c85',
  })
  @IsMongoId()
  @IsNotEmpty()
  itemId: string;

  @ApiProperty({
    description: 'The quantity of the item',
    example: 1,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @IsNotEmpty()
  quantity: number;

  @ApiProperty({
    description: 'The variant ID for products (if applicable)',
    example: '60d21b4667d0d8992e610c86',
    required: false,
  })
  @IsMongoId()
  @IsOptional()
  variantId?: string;

  @ApiProperty({
    description: 'The promotion ID to apply to this item (if applicable)',
    example: '60d21b4667d0d8992e610c87',
    required: false,
  })
  @IsMongoId()
  @IsOptional()
  promotionId?: string;

  @ApiProperty({
    description: 'Additional manual discount to apply to this item',
    required: false,
    type: DiscountDto,
  })
  @IsOptional()
  @Type(() => DiscountDto)
  @ValidateIf((o) => !o.promotionId)
  discount?: DiscountDto;

}
