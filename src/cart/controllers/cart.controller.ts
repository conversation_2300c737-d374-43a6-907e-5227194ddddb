import { Body, Controller, HttpCode, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CartService } from '../services/cart.service';
import { CartRequestDto } from '../dto/cart.request.dto';
import { CartResponseDto } from '../dto/response/cart.response.dto';
import { AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { GetOrganizationId } from 'src/organization/decorators/organization.decorator';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { Response } from 'src/common/response/decorators/response.decorator';
import { IResponse } from 'src/common/response/interfaces/response.interface';

@ApiTags('module.cart')
@ApiBearerAuth()
@Controller('cart')
export class CartController {
  constructor(private readonly cartService: CartService) {}

  @Response("cart.validate")
  @Post('validate')
  @HttpCode(200)
  @ApiOperation({ summary: 'Validate a cart before purchase' })
  @ApiResponse({
    status: 200,
    description: 'The cart has been validated successfully',
    type: CartResponseDto,
  })
  @AuthJwtAccessProtected()
  async validateCart(
    @Body() cartRequestDto: CartRequestDto,
    @GetOrganizationId() organizationId: IDatabaseObjectId
  ): Promise<IResponse<CartResponseDto>> {
    // Ensure the organizationId in the request matches the authenticated organization
    cartRequestDto.organizationId = organizationId.toString();
    const  data = await this.cartService.revalidateCart(cartRequestDto);
    return {
      data
    };
  }
}
