import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsMongoId, IsOptional } from "class-validator";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { ClassType } from "src/utils/enums/class-type.enum";

export class ServicesListDto extends PaginationDto {
    @ApiProperty({
        description: "The class type of the service category.",
        example: ClassType.BOOKINGS,
        required: false,
    })
    @IsOptional()
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "Organizatioon Id",
        example: "66cecb432351713ae4447a6b ",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid organization id" })
    organizationId?: string;

    @ApiProperty({
        description: "service Category Id",
        example: "66cecb432351713ae4447a6b ",
        required: false,
    })
    @IsOptional()

    serviceCategoryId?: string[];

    @ApiProperty({
        description: "Appointment type Array",
        example: "[66cecb432351713ae4447a6b] ",
        required: false,
    })
    @IsOptional()
    appointmentTypes?: string[];
}
