import { ApiProperty } from "@nestjs/swagger";
import { IsObject } from "class-validator";

export class OrganizationSettingsDto {
    @ApiProperty({
        description: "Client onboarding details",
        example: "{}",
        required: true,
    })
    @IsObject({ message: "Required client onboarding options" })
    clientOnboarding: object;

    @ApiProperty({
        description: "Staff onboarding details.",
        example: "{}",
        required: true,
    })
    @IsObject({ message: "Required staff onboarding options" })
    staffOnboarding: object;
}
