import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";

export type ServiceDocument = HydratedDocument<Services>;

@Schema({ timestamps: true })
export class Services {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: String, required: false, default: "" })
    description: string;

    @Prop({ type: String, required: false })
    image: string;

    @Prop({ type: String, required: true, enum: ClassType })
    classType: string;

    @Prop({ type: Boolean, required: true,default:true })
    isActive: Boolean;

    @Prop({
        type: [
            {
                name: String,
                durationInMinutes: Number,
                onlineBookingAllowed: Boolean,
                isActive: { type: Boolean, default: true },
                image: { type: String, required: false },
                isFeatured: { type: Boolean, default: false },
            },
        ],
    })
    appointmentType: { name: String; durationInMinutes: Number; onlineBookingAllowed: Boolean; isActive: Boolean; image?: String, isFeatured?: Boolean }[];

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    createdBy: string;

    @Prop({ type: Boolean, required: false})
    isFeatured?: boolean;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;
}

export const ServiceSchema = SchemaFactory.createForClass(Services);
