import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Pricing } from "../schemas/pricing.schema";
import { Model, Types } from "mongoose";
import { TransactionService } from "src/utils/services/transaction.service";
import { ActiveTimeFrameService } from "src/utils/services/active-time-frame.service";
import { CreatePricingDto } from "../dto/create-pricing.dto";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { PricingListDto } from "../dto/pricing-list.dto";
import { EditPricingDto } from "../dto/edit-pricing.dto";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { OrganizationPipe } from "../pipes/organization.pipe";
import { Services } from "../schemas/services.schema";
import { Purchase, PurchaseDocument } from "src/users/schemas/purchased-packages.schema";
import { PackagePurchaseStatus } from "src/utils/enums/purchase.enum";
import { PricingListByAppointmentTypeDto } from "../dto/pricing-list-by-appointment-type.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PricingListBySubTypeDto } from "../dto/pricing-list-by-subType.dto";
import { DiscountType } from "src/utils/enums/discount.enum";
import { ChangeStatusDto } from "../dto/change-status.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";
import { SessionType } from "src/utils/enums/session-type.enum";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { CreateBundledPricingDto } from "../dto/create-bundle-pricing.dto";
import { UpdateBundledPricingDto } from "../dto/update-bundle-pricing.dto";
import { Clients } from "src/users/schemas/clients.schema";
import { GetPricingActiveListDto } from "../dto/get-pricing-active-list.dto";
import { CopyPricingDto } from "src/organization/dto/copy-pricing.dto";
import { Invoice } from "src/users/schemas/invoice.schema";
import { GetPricingByServiceSubtypeDto } from "../dto/pricingBy-service-subtype.dto";
import { GetServicesByPackageDTO } from "../dto/getService-package.dto";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { PromotionService } from "src/promotions/services/promotion.service";
import { PromotionItemService } from "src/promotions/services/promotion-items.service";
import { ENUM_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { PromotionItemTableName } from "src/promotions/repository/entities/promotion-item.entity";
import { PromotionTableName } from "src/promotions/repository/entities/promotion.entity";
import { Logger } from "nestjs-pino";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
@Injectable()
export class PricingService {
    constructor(
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(Invoice.name) private InvoiceModel: Model<Invoice>,
        @InjectModel(StaffProfileDetails.name) private StaffModel: Model<StaffProfileDetails>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(Purchase.name) private readonly purchaseModel: Model<PurchaseDocument>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        private readonly transactionService: TransactionService,
        private readonly promotionService: PromotionService,
        private readonly promotionItemService: PromotionItemService,
        private readonly activeTimeFrameService: ActiveTimeFrameService,
    ) { }

    async validateDiscount(promotionId: IDatabaseObjectId, organizationId: IDatabaseObjectId, itemValue: number) {
        const promotion = await this.promotionService.findOneById(new Types.ObjectId(promotionId));
        if(!promotion){
            throw new BadRequestException("Invalid promotion id");
        }
        if(promotion.organizationId.toString() !== organizationId.toString()){
            throw new BadRequestException("Invalid promotion id");
        }
        if(!promotion.isActive || promotion.endDate < new Date()){
            throw new BadRequestException("Promotion is not active or expired");
        }
        if(( promotion.type === DiscountType.FLAT && (promotion.value < 0 || promotion.value > itemValue)) || (promotion.type === DiscountType.PERCENTAGE && (promotion.value < 0 || promotion.value > 100))) {
            throw new BadRequestException("Invalid discount value");
        }
        return promotion;
    }

    async createPricingByOrg(createPricingDto: CreatePricingDto, organizationId: IDatabaseObjectId): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const isPricingNameExist = await this.PricingModel.findOne({ name: createPricingDto?.name, organizationId: organizationId })
            if (isPricingNameExist) {
                throw new BadRequestException("Pricing Name is Already Exist");
            }
            const services = createPricingDto.services;
            let query: any = {};
            const flatRelationShip = (services.relationShip || []).map(r => ({
                serviceCategory: new Types.ObjectId(r.serviceCategory),
                subTypeIds: r.subTypeIds.map(id => new Types.ObjectId(id))
            }))

            if (services.type === ClassType.PERSONAL_APPOINTMENT) {
                query = {
                    _id: services.serviceCategory,
                    classType: services.type,
                    "appointmentType._id": { $all: services.appointmentType },
                };
            } else {
                query = {
                    _id: services.serviceCategory,
                    classType: services.type,
                };
            }

            const validateData = await this.ServiceModel.findOne(query);
            if (!validateData) {
                throw new BadRequestException("Invalid service details passed");
            }
            if (createPricingDto?.discount) {
                if (createPricingDto.discount.type === DiscountType.PERCENTAGE && (createPricingDto.discount.value < 0 || createPricingDto.discount.value > 100)) {
                    throw new BadRequestException("Invalid discount value");
                } else if (createPricingDto.discount.type === DiscountType.FLAT && createPricingDto.discount.value < 0) {
                    throw new BadRequestException("Invalid discount value");
                }
            }
            let totalSessions = 0;
            if (services.sessionType === SessionType.DAY_PASS) {
                totalSessions = Number.POSITIVE_INFINITY
                if (!services?.dayPassLimit) {
                    throw new BadRequestException("Day pass limit is required for day pass services");
                }
            }
            else if (services.sessionType === SessionType.UNLIMITED) {
                totalSessions = Number.POSITIVE_INFINITY;
                if (!services?.sessionPerDay) {
                    throw new BadRequestException("Session per day is required for unlimited session type");
                }
            }
            let data = {
                createdBy: organizationId,
                organizationId: organizationId,
                name: createPricingDto.name,
                price: createPricingDto.price,
                isSellOnline: createPricingDto.isSellOnline,
                tax: createPricingDto.tax,
                expiredInDays: createPricingDto.expiredInDays,
                durationUnit: createPricingDto.durationUnit,
                membershipId: createPricingDto?.membershipId,
                hsnOrSacCode: createPricingDto.hsnOrSacCode,
                //membershipIncluded: createPricingDto.membershipIncluded,
                discount: createPricingDto?.discount,
                promotion: createPricingDto?.promotionId,
                revenueCategory: createPricingDto?.revenueCategory,
                services: {
                    type: services.type,
                    serviceCategory: services.serviceCategory ? new Types.ObjectId(services.serviceCategory) : undefined,
                    appointmentType: services.appointmentType && services.appointmentType.length > 0
                        ? services.appointmentType.map(id => new Types.ObjectId(id))
                        : undefined,
                    sessionType: services.sessionType,
                    ...(services.sessionType === SessionType.UNLIMITED && {
                        sessionPerDay: services.sessionPerDay
                    }),
                    ...(services.sessionType === SessionType.DAY_PASS && {
                        sessionPerDay: Number.POSITIVE_INFINITY,
                        dayPassLimit:services.dayPassLimit
                    }),
                    sessionCount: services.sessionType === SessionType.DAY_PASS || services.sessionType === SessionType.UNLIMITED ? totalSessions : services.sessionCount || undefined,
                    introductoryOffer: services?.introductoryOffer || "",
                    revenueCategory: services?.revenueCategory || "",
                    relationShip: flatRelationShip ? flatRelationShip : [],
                },
                activeTimeFrames: createPricingDto?.activeTimeFrames || []
            }
            const addPricing = new this.PricingModel(data);

            if(createPricingDto?.promotionId){
                await this.validateDiscount(createPricingDto.promotionId, organizationId, createPricingDto.price as number);
                await this.promotionItemService.create({
                    organizationId: organizationId,
                    promotion: createPricingDto.promotionId,
                    itemType: ENUM_ITEM_TYPE.SERVICE,
                    item: addPricing._id
                }, { session });
            }

            await addPricing.save({ session });
            await this.transactionService.commitTransaction(session);
            return addPricing;
        } catch (error) {
            console.log(error);

            await this.transactionService.abortTransaction(session);
            if(error instanceof BadRequestException || error instanceof NotFoundException){
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }finally {
            session.endSession();
        }
    }

    async createPricingByStaff(createPricingDto: CreatePricingDto, staffId: IDatabaseObjectId, delegatedId?: IDatabaseObjectId): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
            if (!staffDetails) {
                throw new BadRequestException("Staff not found");
            }

        const orgId = staffDetails.organizationId;
        const services = createPricingDto.services;
        const isPricingNameExist = await this.PricingModel.findOne({ name: createPricingDto?.name, organizationId: orgId })
        if (isPricingNameExist) {
            throw new BadRequestException("Pricing Name is Already Exist");
        }
        let query: any = {};
        const flatRelationShip = (services.relationShip || []).map(r => ({
            serviceCategory: new Types.ObjectId(r.serviceCategory),
            subTypeIds: r.subTypeIds.map(id => new Types.ObjectId(id))
          }))
        if (services.type === ClassType.PERSONAL_APPOINTMENT) {
            query = {
                _id: services.serviceCategory,
                classType: services.type,
                "appointmentType._id": { $all: services.appointmentType },
            };
        } else {
            query = {
                _id: services.serviceCategory,
                classType: services.type,
            };
        }

            const validateData = await this.ServiceModel.findOne(query);

        if (!validateData) {
            throw new BadRequestException("Invalid service details passed");
        }
        if (createPricingDto?.discount) {
            if (createPricingDto.discount.type === DiscountType.PERCENTAGE && (createPricingDto.discount.value < 0 || createPricingDto.discount.value > 100)) {
                throw new BadRequestException("Invalid discount value");
            } else if (createPricingDto.discount.type === DiscountType.FLAT && createPricingDto.discount.value < 0) {
                throw new BadRequestException("Invalid discount value");
            }
        }
        let totalSessions = 0;
        if (services.sessionType === SessionType.DAY_PASS) {
            // let durationUnit = createPricingDto?.durationUnit;
            // let expireIn = createPricingDto.expiredInDays;
            // const startDate = new Date();
            // const endDate = new Date(startDate);

            // switch (durationUnit) {
            //     case DurationUnit.DAYS:
            //         endDate.setDate(endDate.getDate() + expireIn);
            //         break;
            //     case DurationUnit.MONTHS:
            //         endDate.setMonth(endDate.getMonth() + expireIn);
            //         break;
            //     case DurationUnit.YEARS:
            //         endDate.setFullYear(endDate.getFullYear() + expireIn);
            //         break;
            //     default:
            //         throw new Error(`Invalid duration unit: ${durationUnit}`);
            // }

            // const calculateTotalSessions = (start: Date, end: Date): number => {
            //     const timeDifference = end.getTime() - start.getTime();
            //     const totalDays = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));
            //     return totalDays + 1;
            // };


            // totalSessions = calculateTotalSessions(startDate, endDate);
            totalSessions = Number.POSITIVE_INFINITY
            if (!services?.dayPassLimit) {
                throw new BadRequestException("Day pass limit is required for day pass services");
            }

        }
        else if (services.sessionType === SessionType.UNLIMITED) {
            totalSessions = Number.POSITIVE_INFINITY;
            if (!services?.sessionPerDay) {
                throw new BadRequestException("Session per day is required for unlimited session type");
            }
        }
        let data = {
            createdBy: delegatedId || staffId,
            organizationId: orgId,
            name: createPricingDto.name,
            price: createPricingDto.price,
            isSellOnline: createPricingDto.isSellOnline,
            tax: createPricingDto.tax,
            expiredInDays: createPricingDto.expiredInDays,
            durationUnit: createPricingDto.durationUnit,
            membershipId: createPricingDto?.membershipId,
            hsnOrSacCode: createPricingDto.hsnOrSacCode,
            //membershipIncluded: createPricingDto.membershipIncluded,
            discount: createPricingDto?.discount,
            promotion: createPricingDto?.promotionId,
            revenueCategory: createPricingDto?.revenueCategory,
            services: {
                type: services.type,
                ...(services.sessionType === SessionType.UNLIMITED && {
                    sessionPerDay: services.sessionPerDay
                }),
                ...(services.sessionType === SessionType.DAY_PASS && {
                    sessionPerDay: Number.POSITIVE_INFINITY,
                    dayPassLimit:services.dayPassLimit
                }),
                serviceCategory: services.serviceCategory ? new Types.ObjectId(services.serviceCategory) : undefined,
                appointmentType: services.appointmentType && services.appointmentType.length > 0
                    ? services.appointmentType.map(id => new Types.ObjectId(id))
                    : undefined,
                sessionType: services.sessionType,
                sessionCount: services.sessionType === SessionType.DAY_PASS || services.sessionType === SessionType.UNLIMITED ? totalSessions : services.sessionCount || undefined,
                introductoryOffer: services?.introductoryOffer || "",
                revenueCategory: services?.revenueCategory || "",
                relationShip: flatRelationShip ? flatRelationShip : [],
            }
        };

            const addPricing = new this.PricingModel(data);
            if(createPricingDto?.promotionId){
                await this.validateDiscount(createPricingDto.promotionId, orgId as any, createPricingDto.price as number);
                await this.promotionItemService.create({
                    organizationId: orgId,
                    promotion: createPricingDto.promotionId,
                    itemType: ENUM_ITEM_TYPE.SERVICE,
                    item: addPricing._id
                }, { session });
            }
            await addPricing.save( { session });
            await this.transactionService.commitTransaction(session);
            return addPricing;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            console.log(error);
            
            if(error instanceof BadRequestException){
                throw error;
            }
            throw new InternalServerErrorException("Failed to create pricing");
        } finally {
            session.endSession();
        }
    }

    async copyPricing(createPricingDto: CopyPricingDto): Promise<any> {
        const pricingData = await this.PricingModel.findOne({ _id: createPricingDto?.pricingId });
        if (!pricingData) {
            throw new BadRequestException("Pricing not found");
        }

        const baseName = `Copy of ${pricingData.name}`;

        // Find the highest numbered copy
        const latestCopy = await this.PricingModel.findOne({ name: new RegExp(`^${baseName}( \\(\\d+\\))?$`, "i") })
            .sort({ name: -1 }) // Sort in descending order
            .collation({ locale: "en", numericOrdering: true }); // Ensures correct numerical sorting

        let newName = baseName;

        if (latestCopy) {
            const match = latestCopy.name.match(/\((\d+)\)$/);
            const nextNumber = match ? parseInt(match[1]) + 1 : 2;
            newName = `${baseName} (${nextNumber})`;
        }

        return await new this.PricingModel({
            ...pricingData.toObject(),
            name: newName,
            _id: undefined,
        }).save();
    }

    async deletePricing(pricingId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const pricingData = await this.PricingModel.findOne({ _id: pricingId });
            NotFoundException;
            if (!pricingData) throw new NotFoundException("Pricing not found");
            if (pricingData.isBundledPricing) {
                const invoiceData = await this.InvoiceModel.findOne({ "purchaseItems.packageId": pricingData._id });
                const bundlePricingData = await this.PricingModel.findOne({ pricingIds: pricingId });
                if (invoiceData || bundlePricingData) throw new BadRequestException("Pricing is already used and cannot be deleted.");
            } else {
                const usedInBundle = await this.PricingModel.findOne({ pricingIds: pricingId });
                if (usedInBundle) throw new BadRequestException("Pricing is already used in bundled pricing  and cannot be deleted.");
                const purchaseData = await this.purchaseModel.findOne({ packageId: pricingId });
                if (purchaseData) throw new BadRequestException("Pricing is already used  and cannot be deleted.");

            }

            const deletedData = await this.PricingModel.findByIdAndDelete(pricingId).session(session);
            await this.transactionService.commitTransaction(session);
            return {
                message: "Pricing deleted successfully",
                data: deletedData,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    /**
     * Filter pricing packages based on active time frames
     * @param pricingList List of pricing packages to filter
     * @param currentDate Optional date to check against (defaults to current time)
     * @returns Filtered list of pricing packages that are active at the current time
     *
     * Note: Each active time frame document represents either a specific date or a specific day of the week
     * with its own start and end time. Multiple documents can be used to define complex schedules.
     */
    filterByActiveTimeFrames(pricingList: any[], currentDate: Date = new Date()): any[] {
        if (!pricingList || pricingList.length === 0) {
            return [];
        }

        return pricingList.filter(pricing => {
            // If no active time frames are defined, the pricing is always active
            if (!pricing.activeTimeFrames || pricing.activeTimeFrames.length === 0) {
                return true;
            }

            // Check if the pricing is active based on its time frames
            return this.activeTimeFrameService.isActive(pricing.activeTimeFrames, currentDate);
        });
    }

    async pricingListByOrg(pricingListDto: PricingListDto, orgId: string): Promise<any> {
        const pageSize = pricingListDto.pageSize ?? 10;
        const page = pricingListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let countProm = this.PricingModel.countDocuments({ organizationId: orgId, isBundledPricing: { $ne: true }, });
        //let listProm = this.PricingModel.find({ organizationId: orgId }).sort({ createdAt: -1 }).skip(skip).limit(pageSize);
        let listProm = this.PricingModel.aggregate([
            {
                $match: {
                    isBundledPricing: { $ne: true },
                    organizationId: new Types.ObjectId(orgId),
                    ...(pricingListDto.search
                        ? { name: { $regex: pricingListDto.search, $options: "i" } }
                        : {})
                }
            },
            {
                $sort: { createdAt: -1 }
            },
            {
                $lookup: {
                    from: "services",
                    let: {
                        serviceCategoryId:
                            "$services.serviceCategory",
                        relationshipIds: {
                            $ifNull: [
                                "$services.relationShip.serviceCategory",
                                []
                            ]
                        }
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $or: [
                                        {
                                            $eq: [
                                                "$_id",
                                                "$$serviceCategoryId"
                                            ]
                                        },
                                        {
                                            $in: [
                                                "$_id",
                                                "$$relationshipIds"
                                            ]
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                name: 1
                            }
                        }
                    ],
                    as: "serviceCategories"
                }
            },
            {
                $skip: skip
            },
            {
                $limit: pageSize
            },
            {
                $addFields: {
                    serviceCategoryNames: {
                        $map: {
                            input: "$serviceCategories",
                            as: "category",
                            in: "$$category.name"
                        }
                    }
                }
            },
            {
                $project: {
                    serviceCategories: 0
                }
            }
        ])

        let [list, count] = await Promise.all([listProm, countProm]);
        if (list?.length > 0) {
            list.map((element) => {
                const price = Number(element?.price);
                element["discountedValue"] = price
                if (element?.discount?.type === DiscountType.PERCENTAGE) {
                    element["discountedValue"] = price - Number(((element?.discount?.value / 100) * price).toFixed(2));
                } else if (element?.discount?.type === DiscountType.FLAT) {
                    element["discountedValue"] = price - Number((element?.discount?.value ? element?.discount?.value : 0).toFixed(2));
                }
            });
        }
        return {
            list: list,
            count: count,
        };
    }

    async pricingListByStaff(pricingListDto: PricingListDto, staffId: string): Promise<any> {
        const pageSize = pricingListDto.pageSize ?? 10;
        const page = pricingListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        let orgId = staffDetails["organizationId"];

        let countProm = this.PricingModel.countDocuments({ organizationId: orgId, isBundledPricing: { $ne: true }, });
        //let listProm = this.PricingModel.find({ organizationId: orgId }).sort({ createdAt: -1 }).skip(skip).limit(pageSize);
        let listProm = this.PricingModel.aggregate([
            {
                $match: {
                    isBundledPricing: { $ne: true },
                    organizationId: new Types.ObjectId(orgId),
                    ...(pricingListDto.search
                        ? { name: { $regex: pricingListDto.search, $options: "i" } }
                        : {})
                }
            },
            {
                $sort: { createdAt: -1 }
            },
            {
                $lookup: {
                    from: "services",
                    let: {
                        serviceCategoryId:
                            "$services.serviceCategory",
                        relationshipIds: {
                            $ifNull: [
                                "$services.relationShip.serviceCategory",
                                []
                            ]
                        }
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $or: [
                                        {
                                            $eq: [
                                                "$_id",
                                                "$$serviceCategoryId"
                                            ]
                                        },
                                        {
                                            $in: [
                                                "$_id",
                                                "$$relationshipIds"
                                            ]
                                        }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                name: 1
                            }
                        }
                    ],
                    as: "serviceCategories"
                }
            },
            {
                $skip: skip
            },
            {
                $limit: pageSize
            },
            {
                $addFields: {
                    serviceCategoryNames: {
                        $map: {
                            input: "$serviceCategories",
                            as: "category",
                            in: "$$category.name"
                        }
                    }
                }
            },
            {
                $project: {
                    serviceCategories: 0
                }
            }
        ])

        let [list, count] = await Promise.all([listProm, countProm]);
        if (list?.length > 0) {
            list.map((element) => {
                const price = Number(element?.price);
                element["discountedValue"] = price
                if (element?.discount?.type === DiscountType.PERCENTAGE) {
                    element["discountedValue"] = price - Number(((element?.discount?.value / 100) * price).toFixed(2));
                } else if (element?.discount?.type === DiscountType.FLAT) {
                    element["discountedValue"] = price - Number((element?.discount?.value ? element?.discount?.value : 0).toFixed(2));
                }
            });
        }
        return {
            list: list,
            count: count,
        };
    }

    async updatePricingByOrg(updatePricingDto: EditPricingDto, orgId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const checkPricing = await this.PricingModel.findOne({
                _id: updatePricingDto.pricingId,
                organizationId: orgId,
            });
            if (!checkPricing) {
                throw new BadRequestException("Pricing not found");
            }
            const isPricingNameExist = await this.PricingModel.findOne({
                name: updatePricingDto?.name,
                organizationId: orgId,
                _id: { $ne:updatePricingDto.pricingId }
            });

            if (isPricingNameExist) {
                throw new BadRequestException("Pricing Name is Already Exist");
            }
            const services = updatePricingDto.services;
            const flatRelationShip = (services.relationShip || []).map(r => ({
                serviceCategory: new Types.ObjectId(r.serviceCategory),
                subTypeIds: r.subTypeIds.map(id => new Types.ObjectId(id))
            }))

            const query: any = {
                _id: services.serviceCategory,
                classType: services.type,
            };

            if (services.type === ClassType.PERSONAL_APPOINTMENT) {
                query["appointmentType._id"] = { $all: services.appointmentType };
            }

        const validateData = await this.ServiceModel.findOne(query);
        if (!validateData) {
            throw new BadRequestException("Invalid service details passed");
        }
        if (updatePricingDto?.discount) {
            if (updatePricingDto.discount.type === DiscountType.PERCENTAGE && (updatePricingDto.discount.value < 0 || updatePricingDto.discount.value > 100)) {
                throw new BadRequestException("Invalid discount value");
            } else if (updatePricingDto.discount.type === DiscountType.FLAT && updatePricingDto.discount.value < 0) {
                throw new BadRequestException("Invalid discount value");
            }
        }
        let totalSessions = 0;
        if (services.sessionType === SessionType.DAY_PASS) {
            totalSessions = Number.POSITIVE_INFINITY
            if (!services?.dayPassLimit) {
                throw new BadRequestException("Day pass limit is required for day pass services");
            }
        }
        else if (services.sessionType === SessionType.UNLIMITED) {
            totalSessions = Number.POSITIVE_INFINITY;
            if (!services?.sessionPerDay) {
                throw new BadRequestException("Session per day is required for unlimited session type");
            }
        }

        const data = {
            name: updatePricingDto.name,
            price: updatePricingDto.price,
            isSellOnline: updatePricingDto.isSellOnline,
            tax: updatePricingDto.tax,
            expiredInDays: updatePricingDto.expiredInDays,
            durationUnit: updatePricingDto.durationUnit,
            membershipId: updatePricingDto?.membershipId,
            hsnOrSacCode: updatePricingDto.hsnOrSacCode,
            //membershipIncluded: updatePricingDto.membershipIncluded,
            discount: updatePricingDto?.discount,
            promotion: updatePricingDto?.promotionId || null,
            revenueCategory: updatePricingDto?.revenueCategory,
            services: {
                type: services.type,
                ...(services.sessionType === SessionType.UNLIMITED && {
                    sessionPerDay: services.sessionPerDay
                }),
                ...(services.sessionType === SessionType.DAY_PASS && {
                    sessionPerDay: Number.POSITIVE_INFINITY,
                    dayPassLimit:services.dayPassLimit
                }),
                serviceCategory: services.serviceCategory ? new Types.ObjectId(services.serviceCategory) : undefined,
                appointmentType: services.appointmentType && services.appointmentType.length > 0
                    ? services.appointmentType.map(id => new Types.ObjectId(id))
                    : undefined,
                sessionType: services.sessionType,
                sessionCount: services.sessionType === SessionType.DAY_PASS || services.sessionType === SessionType.UNLIMITED ? totalSessions : services.sessionCount || undefined,
                introductoryOffer: services?.introductoryOffer || "",
                revenueCategory: services?.revenueCategory || "",
            },
            activeTimeFrames: updatePricingDto?.activeTimeFrames || []
        };
        if(updatePricingDto?.promotionId){
            await this.validateDiscount(updatePricingDto.promotionId, new Types.ObjectId(orgId), updatePricingDto.price as number);
            const isExist = await this.promotionItemService.getTotal({
                promotion: updatePricingDto.promotionId,
                item: new Types.ObjectId(updatePricingDto.pricingId)
            });
            if(!isExist){
                await this.promotionItemService.create({
                    organizationId: new Types.ObjectId(orgId),
                    promotion: updatePricingDto.promotionId,
                    itemType: ENUM_ITEM_TYPE.SERVICE,
                    item: new Types.ObjectId(updatePricingDto.pricingId)
                }, { session });
            }
        }
        const result = await this.PricingModel.findOneAndUpdate(
            { _id: updatePricingDto.pricingId },
            { $set: data },
            { new: true, session },
        );
        await this.transactionService.commitTransaction(session);
        return result;
        }
        catch (error) {
            await this.transactionService.abortTransaction(session);
            if (error instanceof BadRequestException || error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException('Failed to update pricing');
        } finally {
            session.endSession();
        }
    }

    async updatePricingByStaff(updatePricingDto: EditPricingDto, staffId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
        const staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) throw new BadRequestException("Staff not found");
        const orgId = staffDetails.organizationId;
        const checkPricing = await this.PricingModel.findOne({
            _id: updatePricingDto.pricingId,
            organizationId: orgId,
        });
        if (!checkPricing) throw new BadRequestException("Pricing not found");
        const isPricingNameExist = await this.PricingModel.findOne({
            name: updatePricingDto?.name,
            organizationId: orgId,
            _id: { $ne:updatePricingDto.pricingId }
        });
        if (isPricingNameExist) {
            throw new BadRequestException("Pricing Name is Already Exist");
        }

        const services = updatePricingDto.services;

        const query: any = {
            _id: services.serviceCategory,
            classType: services.type,
        };
        const flatRelationShip = (services.relationShip || []).map(r => ({
            serviceCategory: new Types.ObjectId(r.serviceCategory),
            subTypeIds: r.subTypeIds.map(id => new Types.ObjectId(id))
          }))
        if (services.type === ClassType.PERSONAL_APPOINTMENT) {
            query["appointmentType._id"] = { $all: services.appointmentType };
        }

        const validateData = await this.ServiceModel.findOne(query);

        if (!validateData) {
            throw new BadRequestException("Invalid service details passed");
        }
        if (updatePricingDto?.discount) {
            if (updatePricingDto.discount.type === DiscountType.PERCENTAGE && (updatePricingDto.discount.value < 0 || updatePricingDto.discount.value > 100)) {
                throw new BadRequestException("Invalid discount value");
            } else if (updatePricingDto.discount.type === DiscountType.FLAT && updatePricingDto.discount.value < 0) {
                throw new BadRequestException("Invalid discount value");
            }
        }

        let totalSessions = 0;
        if (services.sessionType === SessionType.DAY_PASS) {
            // let durationUnit = updatePricingDto?.durationUnit;
            // let expireIn = updatePricingDto.expiredInDays;
            // const startDate = new Date();
            // const endDate = new Date(startDate);

            // switch (durationUnit) {
            //     case DurationUnit.DAYS:
            //         endDate.setDate(endDate.getDate() + expireIn);
            //         break;
            //     case DurationUnit.MONTHS:
            //         endDate.setMonth(endDate.getMonth() + expireIn);
            //         break;
            //     case DurationUnit.YEARS:
            //         endDate.setFullYear(endDate.getFullYear() + expireIn);
            //         break;
            //     default:
            //         throw new Error(`Invalid duration unit: ${durationUnit}`);
            // }

            // const calculateTotalSessions = (start: Date, end: Date): number => {
            //     const timeDifference = end.getTime() - start.getTime();
            //     const totalDays = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));
            //     return totalDays + 1;
            // };


            // totalSessions = calculateTotalSessions(startDate, endDate);
            totalSessions = Number.POSITIVE_INFINITY
            if (!services?.dayPassLimit) {
                throw new BadRequestException("Day pass limit is required for day pass services");
            }
        }
        else if (services.sessionType === SessionType.UNLIMITED) {
            totalSessions = Number.POSITIVE_INFINITY;
            if (!services?.sessionPerDay) {
                throw new BadRequestException("Session per day is required for unlimited session type");
            }
        }
        const data = {
            name: updatePricingDto.name,
            price: updatePricingDto.price,
            isSellOnline: updatePricingDto.isSellOnline,
            tax: updatePricingDto.tax,
            expiredInDays: updatePricingDto.expiredInDays,
            durationUnit: updatePricingDto.durationUnit,
            membershipId: updatePricingDto?.membershipId,
            hsnOrSacCode: updatePricingDto.hsnOrSacCode,
            //membershipIncluded: updatePricingDto.membershipIncluded,
            discount: updatePricingDto?.discount,
            promotion: updatePricingDto?.promotionId || null,
            revenueCategory: updatePricingDto?.revenueCategory,
            services: {
                type: services.type,
                ...(services.sessionType === SessionType.UNLIMITED && {
                    sessionPerDay: services.sessionPerDay
                }),
                ...(services.sessionType === SessionType.DAY_PASS && {
                    sessionPerDay: Number.POSITIVE_INFINITY,
                    dayPassLimit:services.dayPassLimit
                }),
                serviceCategory: services.serviceCategory ? new Types.ObjectId(services.serviceCategory) : undefined,
                appointmentType: services.appointmentType && services.appointmentType.length > 0
                    ? services.appointmentType.map(id => new Types.ObjectId(id))
                    : undefined,
                sessionType: services.sessionType,
                sessionCount: services.sessionType === SessionType.DAY_PASS || services.sessionType === SessionType.UNLIMITED ? totalSessions : services.sessionCount || undefined,
                introductoryOffer: services?.introductoryOffer || "",
                revenueCategory: services?.revenueCategory || "",
                relationShip:flatRelationShip ? flatRelationShip : [],

            },
            activeTimeFrames: updatePricingDto?.activeTimeFrames || []
        };
        if(updatePricingDto?.promotionId){
            await this.validateDiscount(updatePricingDto.promotionId, new Types.ObjectId(orgId), updatePricingDto.price as number);
            const isExist = await this.promotionItemService.getTotal({
                promotion: updatePricingDto.promotionId,
                item: new Types.ObjectId(updatePricingDto.pricingId)
            });
            if(!isExist){
                await this.promotionItemService.create({
                    organizationId: new Types.ObjectId(orgId),
                    promotion: updatePricingDto.promotionId,
                    itemType: ENUM_ITEM_TYPE.SERVICE,
                    item: new Types.ObjectId(updatePricingDto.pricingId)
                }, { session });
            }
        }
        const result = await this.PricingModel.findOneAndUpdate(
            { _id: updatePricingDto.pricingId },
            { $set: data },
            { new: true, session },
        );

        return result;
        } catch (error) {
            await session.abortTransaction();
            if (error instanceof BadRequestException || error instanceof NotFoundException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        } finally {
            session.endSession();
        }
    }

    async pricingDetails(pricingId: string): Promise<any> {
        const result = await this.PricingModel.findOne({ _id: pricingId }).lean()
        if (!result) {
            throw new BadRequestException("Pricing not found");
        }
        const promotion = await this.promotionService.findOne({
            _id: result.promotion,
            isActive: true,
            // startDate: { $lte: new Date() },
            // endDate: { $gte: new Date() }
        }, { select: "name couponCode description value isActive startDate endDate" });
        result["promotion"] = promotion?.toObject() || null;
        if (result?.isBundledPricing == true) {
            return result
        }
        else {
            const services = result.services;
            const query: any = {
                _id: services.serviceCategory,
            };

            if (services.type === ClassType.PERSONAL_APPOINTMENT) {
                query["appointmentType._id"] = { $all: services.appointmentType };
            }

            const serviceCategoryDetails = await this.ServiceModel.findOne(query, {
                name: 1,
                appointmentType: 1,
            });

            if (!serviceCategoryDetails) {
                throw new BadRequestException("Service category details not found");
            }
            result.services["serviceCategoryName"] = serviceCategoryDetails.name;

            if (services.type === ClassType.PERSONAL_APPOINTMENT) {
                const appointmentTypes = services.appointmentType.map((appointmentId) => {
                    const matchingAppointment = serviceCategoryDetails.appointmentType.find(
                        (appointment) => appointment["_id"].toString() === appointmentId.toString()
                    );
                    if (matchingAppointment) {
                        return {
                            appointmentType: appointmentId,
                            name: matchingAppointment.name,
                        };
                    }
                    return null;
                });

                result.services["appointmentTypeDetails"] = appointmentTypes.filter(
                    (item) => item !== null
                );
            }
        }
        return result;
    }

    // async pricingListByServiceOrg(organizationId: string, serviceCategoryId: string): Promise<any> {
    //     let pipeline = this.organizationPipes.pricingListByServiceOrg(organizationId);
    //     let list = await this.PricingModel.aggregate(pipeline);
    //     return list;
    // }

    // async pricingListByServiceStaff(staffId: string, serviceCategoryId: string): Promise<any> {
    //     let staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
    //     if (!staffDetails) throw new BadRequestException("Staff not found");
    //     let orgId = staffDetails["organizationId"];
    //     let pipeline = this.organizationPipes.pricingListByServiceStaff(orgId);
    //     let list = await this.PricingModel.aggregate(pipeline);
    //     return list;
    // }

    // async pricingListByService(pricingListDto: PricingListByAppointmentTypeDto): Promise<any> {
    //     const query: any = {
    //         $or: [],
    //     };

    //     const firstCondition: any = {
    //         $and: [
    //             { "services.serviceCategory": new Types.ObjectId(pricingListDto.serviceId) },
    //         ],
    //     };
    //     if (pricingListDto.appointmentId) {
    //         firstCondition.$and.push({
    //             "services.appointmentType": new Types.ObjectId(pricingListDto.appointmentId),
    //         });
    //     }

    //     const secondCondition: any = {
    //         $and: [
    //             {
    //                 "services.relationShip": {
    //                     $elemMatch: {
    //                         serviceCategory: new Types.ObjectId(pricingListDto.serviceId),
    //                         ...(pricingListDto.appointmentId && {
    //                             subTypeIds: new Types.ObjectId(pricingListDto.appointmentId),
    //                         }),
    //                     },
    //                 },
    //             },
    //         ],
    //     };

    //     query.$or.push(firstCondition, secondCondition);

    //     const list = await this.PricingModel.find(query, {
    //         name: 1,
    //         price: 1,
    //         tax: 1,
    //         createdAt: 1,
    //     }).sort({ createdAt: -1 });

    //     return list;
    // }

    async pricingListByService(pricingListDto: PricingListByAppointmentTypeDto): Promise<any> {
        const query: any = {
            $or: [],
        };

        const firstCondition: any = {
            $and: [
                { "services.serviceCategory": new Types.ObjectId(pricingListDto.serviceId) },
            ],
        };
        if (pricingListDto.appointmentId) {
            firstCondition.$and.push({
                "services.appointmentType": new Types.ObjectId(pricingListDto.appointmentId),
            });
        }

        const secondCondition: any = {
            $and: [
                {
                    "services.relationShip": {
                        $elemMatch: {
                            serviceCategory: new Types.ObjectId(pricingListDto.serviceId),
                            ...(pricingListDto.appointmentId && {
                                subTypeIds: new Types.ObjectId(pricingListDto.appointmentId),
                            }),
                        },
                    },
                },
            ],
        };

        query.$or.push(firstCondition, secondCondition);

        const list = await this.PricingModel.find(query, {
            name: 1,
            price: 1,
            tax: 1,
            isActive: 1,
            createdAt: 1,
            services: 1,
        }).sort({ createdAt: -1 });

        const modifiedList = list.map((item) => {
            const isAssigned = item.services.relationShip?.some((relation) => {
                const isServiceMatch = relation.serviceCategory.toString() === pricingListDto.serviceId;
                const isAppointmentMatch = !pricingListDto.appointmentId || relation.subTypeIds?.some((id) => id.toString() === pricingListDto.appointmentId);
                return isServiceMatch && isAppointmentMatch;
            });

            return {
                ...item.toObject(),
                isAssigned: isAssigned || false,
            };
        });

        return modifiedList;
    }

    async pricingListByUser(userID: string,user:IUserDocument, classType?: string, staffId?: string,isForSharePass?:boolean): Promise<any> {
        const { role } = user
        if (classType && classType !== ClassType.BOOKINGS && !staffId) {
            throw new BadRequestException("StaffId is required for this class type");
        }
        let orgId
        if([ENUM_ROLE_TYPE.TRAINER,ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN].includes(role.type)){
        const staffDetails = await this.StaffModel.findOne({ userId: user._id }, { organizationId: 1 });
        if (!staffDetails) throw new BadRequestException("Not able to find Organization for this user");
        orgId = staffDetails.organizationId;
        }
        if(ENUM_ROLE_TYPE.ORGANIZATION==role.type){
        orgId = user._id;

        }
        const query: any = {
            userId: new Types.ObjectId(userID),
            organizationId: new Types.ObjectId(orgId),
            isExpired: false,
            startDate: { $lte: new Date() },
            endDate: { $gte: new Date() },
        };

        if (isForSharePass) {
            query.sessionType = "multiple";
            query.$or = [{ remainingSessions: { $gt: 0 } }, { remainingSessions: { $ne: "unlimited" } }];
        }

        const agg: any[] = [
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails",
                },
            },
            {
                $unwind: {
                    path: "$pricingDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    remainingSessions: {
                        $cond: {
                            if: { $eq: ["$pricingDetails.services.sessionCount", Infinity] },
                            then: "unlimited",
                            else: {
                                $subtract: [
                                    {
                                        $cond: {
                                            if: { $eq: ["$sharePass", true] },
                                            then: "$totalSessions",
                                            else: "$pricingDetails.services.sessionCount",
                                        },
                                    },
                                    "$sessionConsumed",
                                ],
                            },
                        },
                    },
                },
            },
            {
                $match: query,
            },
        ];

        if (classType) {
            agg.push({
                $match: {
                    "pricingDetails.services.type": classType
                }
            });

            if (classType !== ClassType.BOOKINGS) {
                agg.push({
                    $lookup: {
                        from: "payrates",
                        let: {
                            userId: new Types.ObjectId(staffId),
                            serviceType: classType,
                            serviceCategory: "$pricingDetails.services.serviceCategory",
                            appointmentTypes: "$pricingDetails.services.appointmentType",
                            relationships: {
                                $ifNull: ["$pricingDetails.services.relationShip", []]
                            }
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ["$userId", "$$userId"] },
                                            { $eq: ["$serviceType", "$$serviceType"] },
                                            {
                                                $or: [
                                                    {
                                                        $and: [{ $eq: ["$serviceCategory", "$$serviceCategory"] },{ $in: ["$appointmentType", "$$appointmentTypes"] }]
                                                    },
                                                    {
                                                        $anyElementTrue: {
                                                            $map: {
                                                                input: "$$relationships",
                                                                as: "rel",
                                                                in: {
                                                                    $and: [
                                                                        { $eq: ["$serviceCategory", "$$rel.serviceCategory"] },
                                                                        { $in: ["$appointmentType", "$$rel.subTypeIds"] }
                                                                    ]
                                                                }
                                                            }
                                                        }
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                }
                            },
                            {
                                $project: {
                                    appointmentType: 1,
                                    serviceCategory: 1
                                }
                            }
                        ],
                        as: "payRateDetails"
                    }
                });

                agg.push({
                    $match: {
                        payRateDetails: { $ne: [] }
                    }
                });
            }
        }

        agg.push(
            {
                $set: {
                    sessionConsumed: { $cond: ['$sessionConsumed', '$sessionConsumed', 0] },
                }
            },
            {
                $project: {
                    packageId: "$pricingDetails._id",
                    packageName: "$pricingDetails.name",
                    sessionCount: {
                        $cond: {
                            if: { $eq: ["$pricingDetails.services.sessionCount", Infinity] },
                            then: "unlimited",
                            else: "$pricingDetails.services.sessionCount",
                        },
                    },
                    sessionConsumed: "$sessionConsumed",
                    remainingSession: "$remainingSessions",
                },
            },
        );

        const checkActivePackages = await this.purchaseModel.aggregate(agg);

        return checkActivePackages;
    }

    async getServicesListByPackageId(packageId: string): Promise<any> {
        const agg: any[] = [
            {
                $match: {
                    _id: new Types.ObjectId(packageId),
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "services.serviceCategory",
                    foreignField: "_id",
                    as: "serviceCategoryData"
                }
            },
            {
                $unwind: {
                    path: "$serviceCategoryData",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    "serviceCategoryData.appointmentType": {
                        $filter: {
                            input:
                                "$serviceCategoryData.appointmentType",
                            as: "apptType",
                            cond: {
                                $and: [
                                    {
                                        $in: [
                                            "$$apptType._id",
                                            "$services.appointmentType"
                                        ]
                                    },
                                    {
                                        $eq: ["$$apptType.isActive", true]
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $addFields: {
                    "services.relationShip": {
                        $ifNull: ["$services.relationShip", []] // Handle empty or missing relationShip array
                    }
                }
            },
            {
                $unwind: {
                    path: "$services.relationShip",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "services",
                    localField:
                        "services.relationShip.serviceCategory",
                    foreignField: "_id",
                    as: "relationshipServiceData"
                }
            },
            {
                $unwind: {
                    path: "$relationshipServiceData",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    "relationshipServiceData.subTypeIds": {
                        $filter: {
                            input:
                                "$relationshipServiceData.appointmentType",
                            as: "subType",
                            cond: {
                                $and: [
                                    {
                                        $in: [
                                            "$$subType._id",
                                            "$services.relationShip.subTypeIds"
                                        ]
                                    },
                                    {
                                        $eq: ["$$subType.isActive", true]
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $group: {
                    _id: "$_id",
                    services: {
                        $push: {
                            _id: "$serviceCategoryData._id",
                            name: "$serviceCategoryData.name",
                            appointmentType: {
                                $ifNull: [
                                    "$serviceCategoryData.appointmentType",
                                    []
                                ]
                            }
                        }
                    },
                    relationshipServices: {
                        $push: {
                            _id: "$relationshipServiceData._id",
                            name: "$relationshipServiceData.name",
                            appointmentType: {
                                $ifNull: [
                                    "$relationshipServiceData.subTypeIds",
                                    []
                                ]
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 1,
                    services: {
                        $concatArrays: [
                            "$services",
                            {
                                $filter: {
                                    input: "$relationshipServices",
                                    as: "relService",
                                    cond: {
                                        $gt: [
                                            {
                                                $size: {
                                                    $ifNull: [
                                                        "$$relService.appointmentType",
                                                        []
                                                    ]
                                                }
                                            },
                                            0
                                        ]
                                    }
                                }
                            }
                        ]
                    }
                }
            },
            {
                $unwind: "$services"
              },
              {
                $group: {
                  _id: "$services._id",
                  packageId: {
                    $first: "$_id"
                  },
                  name: {
                    $first: "$services.name"
                  },
                  appointmentType: {
                    $push: "$services.appointmentType"
                  }
                }
              },
              {
                $group: {
                  _id: "$packageId",
                  services: {
                    $push: "$$ROOT"
                  }
                }
              },
              {
                $project: {
                  _id: 1,
                  services: {
                    $map: {
                      input: "$services",
                      as: "service",
                      in: {
                        _id: "$$service._id",
                        // packageId: "$$service.packageId",
                        name: "$$service.name",
                        appointmentType: {
                          $reduce: {
                            input:
                              "$$service.appointmentType",
                            initialValue: [],
                            in: {
                              $concatArrays: [
                                "$$value",
                                "$$this"
                              ]
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
        ]
        const servicesList = await this.PricingModel.aggregate(agg)

        return servicesList.length > 0 ? servicesList[0].services : [];
    }

    async pricingListBySubType(pricingListDto: PricingListBySubTypeDto): Promise<any> {
    let { organizationId, serviceId, subTypeId, classType, pageSize, page } = pricingListDto;

    page = isNaN(page) || page < 1 ? 1 : page;
    pageSize = isNaN(pageSize) || pageSize < 1 ? 10 : pageSize;
    const skip = (page - 1) * pageSize;

    const matchFilter = {
        isActive: true,
        "services.type": classType,
        organizationId: new Types.ObjectId(organizationId),
        isBundledPricing: { $ne: true },
        "services.relationShip": {
                    $not: {
                        $elemMatch: {
                            serviceCategory: new Types.ObjectId(serviceId),
                            ...(subTypeId ? { subTypeIds: { $elemMatch: { $eq: new Types.ObjectId(subTypeId) } } } : {}),
                        },
                    },
                },

    };

    const pipeline: any[] = [
        { $unwind: "$services" },
        { $match: matchFilter },
        {
            $lookup: {
                from: "services",
                localField: "services.serviceCategory",
                foreignField: "_id",
                as: "serviceCategoryDetails"
            }
        },
        {
            $unwind: {
                path: "$serviceCategoryDetails",
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $project: {
                pricingOptionName: "$name",
                sessionType: "$services.sessionType",
                sessionCount: "$services.sessionCount",
                classType: "$services.type",
                serviceCategoryName: "$serviceCategoryDetails.name",
                price: 1,
                isSellOnline: 1,
                createdAt: 1,
                updatedAt: 1,
                isActive: 1
            }
        },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: pageSize }
    ];

    const countPipeline: any[] = [
        { $unwind: "$services" },
        { $match: matchFilter },
        { $count: "total" }
    ];

    const [list, countResult] = await Promise.all([
        this.PricingModel.aggregate(pipeline).exec(),
        this.PricingModel.aggregate(countPipeline).exec()
    ]);

    const total = countResult.length > 0 ? countResult[0].total : 0;

    return { list, total, page };
}

    // async getServicesListByPackageId(packageId: string): Promise<any> {
    //     const checkActivePackages = await this.PricingModel.aggregate([
    //         {
    //             $match: {
    //                 _id: new Types.ObjectId(packageId),
    //             },
    //         },
    //         {
    //             $unwind: "$services",
    //         },
    //         {
    //             $match: {
    //                 "services.type": "personalAppointment",
    //             },
    //         },
    //         {
    //             $group: {
    //                 _id: null,
    //                 serviceCategories: {
    //                     $addToSet: "$services.appointmentType",
    //                 },
    //             },
    //         },
    //         {
    //             $project: {
    //                 _id: 0,
    //                 serviceCategories: 1,
    //             },
    //         },
    //     ]);
    //     if (checkActivePackages.length > 0) {
    //         const services = await this.ServiceModel.aggregate([
    //             {
    //                 $match: {
    //                     "appointmentType._id": { $in: checkActivePackages[0].serviceCategories.map((id) => new Types.ObjectId(id)) },
    //                 },
    //             },
    //             {
    //                 $project: {
    //                     _id: 1,
    //                     name: 1,
    //                     appointmentType: {
    //                         $filter: {
    //                             input: "$appointmentType",
    //                             as: "type",
    //                             cond: { $in: ["$$type._id", checkActivePackages[0].serviceCategories.map((id) => new Types.ObjectId(id))] },
    //                         },
    //                     },
    //                 },
    //             },
    //         ]);
    //         return services;
    //     }
    // }

    async activePricingListByOrg(paginationDto: PaginationDTO, orgId: IDatabaseObjectId): Promise<any> {
        const pageSize = paginationDto.pageSize ?? 10;
        const page = paginationDto.page ?? 1;
        const skip = pageSize * (page - 1);

        const searchQuery: any = {
            organizationId: orgId,
            isActive: true
        };

        if (paginationDto.search) {
            searchQuery.name = { $regex: paginationDto.search, $options: "i" };
        }

        if (paginationDto?.fetchWithBundled === false) {
            searchQuery.isBundledPricing = { $ne: true };
        }

        const countProm = this.PricingModel.countDocuments(searchQuery);
        const listProm = this.PricingModel.find(searchQuery)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(pageSize)
            .lean();

        const [list, count] = await Promise.all([listProm, countProm]);

        const promotionIds = list.map(item => item.promotion);
        const promotions = await this.promotionService.findAll({
            _id: { $in: promotionIds },
            isActive: true,
            // startDate: { $lte: new Date() },
            // endDate: { $gte: new Date() }
        });

        list.forEach(item => {
            item["promotion"] = promotions.find(promotion => promotion._id.equals(item.promotion))?.toObject() || null;
        });

        if (paginationDto?.fetchWithBundled === true && list?.length > 0) {
            for (const element of list) {
                let price = Number(element.price);
                if('promotion' in element){
                    const promotion: any = element.promotion;
                    element["discountedValue"] = await this.promotionService.calculateDiscountedPrice(price, {type: promotion?.type, value: promotion?.value});
                } else {
                    if (element?.discount?.type === DiscountType.PERCENTAGE) {
                        element["discountedValue"] = Number(((element.discount.value / 100) * price).toFixed(2));
                    } else if (element?.discount?.type === DiscountType.FLAT) {
                        element["discountedValue"] = Number((element?.discount?.value ? element?.discount?.value : 0).toFixed(2));
                    } else {
                        element["discountedValue"] = 0.00;
                    }

                    if (element.isBundledPricing) {
                        const bundledPricing = await this.PricingModel.find({ _id: { $in: element.pricingIds } })
                            .select("services.type")
                            .lean();
                        element["type"] = [
                            ...new Set(bundledPricing.flatMap(bp => bp.services?.type ?? []))
                        ].join(", ");
                    }
                }
            }
        }

        return {
            list,
            count,
        };
    }

    async activePricingListByStaff(paginationDto: PaginationDTO, staffId: IDatabaseObjectId): Promise<any> {
        const pageSize = paginationDto.pageSize ?? 10;
        const page = paginationDto.page ?? 1;
        const skip = pageSize * (page - 1);

        const staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        const orgId = staffDetails["organizationId"];

        const searchQuery: any = {
            organizationId: orgId,
            isActive: true
        };

        if (paginationDto.search) {
            searchQuery.name = { $regex: paginationDto.search, $options: "i" };
        }

        if (paginationDto?.fetchWithBundled === false) {
            searchQuery.isBundledPricing = { $ne: true };
        }

        const countProm = this.PricingModel.countDocuments(searchQuery);
        const listProm = this.PricingModel.find(searchQuery)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(pageSize)
            .lean();

        const [list, count] = await Promise.all([listProm, countProm]);

        if (paginationDto?.fetchWithBundled === true && list?.length > 0) {
            for (const element of list) {
                let price = Number(element.price);
                if (element?.discount?.type === DiscountType.PERCENTAGE) {
                    element["discountedValue"] = Number(((element.discount.value / 100) * price).toFixed(2));
                } else if (element?.discount?.type === DiscountType.FLAT) {
                    element["discountedValue"] = Number((element?.discount?.value ? element?.discount?.value : 0).toFixed(2));
                } else {
                    element["discountedValue"] = 0.00;
                }

                if (element.isBundledPricing) {
                    const bundledPricing = await this.PricingModel.find({ _id: { $in: element.pricingIds } })
                        .select("services.type")
                        .lean();
                    element["type"] = [
                        ...new Set(bundledPricing.flatMap(bp => bp.services?.type ?? []))
                    ].join(", ");
                }
            }
        }

        return {
            list,
            count,
        };
    }

    async activePricingListByUser(orgId: IDatabaseObjectId, body: GetPricingActiveListDto, clientId: string): Promise<any> {
        const pageSize = body.pageSize ?? 10;
        const page = body.page ?? 1;
        const skip = pageSize * (page - 1);

        // const clientDetails = await this.ClientModel.findOne({ userId: clientId }, { organizationId: 1 });
        // if (!clientDetails) throw new BadRequestException("Client not found");
        // const orgId = clientDetails["organizationId"];

        const matchStage: any = {
            organizationId: orgId,
            isActive: true,
            isSellOnline: true
        };

        if (body.search) {
            matchStage.name = { $regex: body.search, $options: "i" };
        }

        if (body?.fetchWithBundled === false) {
            matchStage.isBundledPricing = { $ne: true };
        }

        if (body?.classType) {
            matchStage['services.type'] = body.classType;
        }

        if (body?.fetchMemberships === true) {
            matchStage["membershipId"] = { $exists: true };
        }

        const aggregationPipeline: any[] = [
            { $match: matchStage },
            { $sort: { createdAt: -1 } },
            { $skip: skip },
            { $limit: pageSize },
            {
                $lookup: {
                    from: 'services',
                    localField: 'services.serviceCategory',
                    foreignField: '_id',
                    as: 'serviceCategoryDetails'
                }
            },
            {
                $lookup: {
                    from: PromotionItemTableName,
                    let: { pricingId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                itemType: ENUM_ITEM_TYPE.SERVICE,
                                $expr: {
                                    $and: [
                                        { $eq: ['$item', '$$pricingId'] }
                                    ]
                                }
                            }
                        },
                        {
                            $lookup: {
                                from: PromotionTableName,
                                localField: 'promotion',
                                foreignField: '_id',
                                pipeline: [
                                    {
                                        $match: {
                                            isActive: true,
                                            // startDate: { $lte: new Date() },
                                            // endDate: { $gte: new Date() }
                                        }
                                    }
                                ],
                                as: 'promotionDetails'
                            }
                        },
                        {
                            $unwind: {
                                path: '$promotionDetails',
                                preserveNullAndEmptyArrays: true
                            }
                        },
                        {
                            $project: {
                                _id: '$promotionDetails._id',
                                name: '$promotionDetails.name',
                                description: '$promotionDetails.description',
                                type: '$promotionDetails.type',
                                value: '$promotionDetails.value',
                                startDate: '$promotionDetails.startDate',
                                endDate: '$promotionDetails.endDate'
                            }
                        }
                    ],
                    as: 'promotions'
                }
            },
            {
                $addFields: {
                    serviceCategoryName: {
                        $arrayElemAt: ["$serviceCategoryDetails.name", 0]
                    }
                }
            },
            {
                $project: {
                    serviceCategoryDetails: 0 // remove unwanted joined field
                }
            }
        ];

        let [list, count] = await Promise.all([
            this.PricingModel.aggregate(aggregationPipeline),
            this.PricingModel.countDocuments(matchStage)
        ]);

        // Filter pricing packages based on active time frames
        list = this.filterByActiveTimeFrames(list);

        // Recalculate count if we filtered out any items
        if (list.length < count) {
            count = list.length;
        }

        if (body?.fetchWithBundled === true && list?.length > 0) {
            for (const element of list) {
                let price = Number(element.price);
                if (element?.discount?.type === DiscountType.PERCENTAGE) {
                    element["discountedValue"] = Number(((element.discount.value / 100) * price).toFixed(2));
                } else if (element?.discount?.type === DiscountType.FLAT) {
                    element["discountedValue"] = Number((element?.discount?.value ? element?.discount?.value : 0).toFixed(2));
                } else {
                    element["discountedValue"] = 0.00;
                }

                if (element.isBundledPricing) {
                    const bundledPricing = await this.PricingModel.find({ _id: { $in: element.pricingIds } })
                        .select("services.type")
                        .lean();
                    element["type"] = [
                        ...new Set(bundledPricing.flatMap(bp => bp.services?.type ?? []))
                    ].join(", ");
                }
            }
        }

        return {
            list,
            count,
        };
    }

    async activePricingListByUserForApp(body: GetPricingActiveListDto): Promise<any> {
        const pageSize = body.pageSize ?? 10;
        const page = body.page ?? 1;
        const skip = pageSize * (page - 1);
        if(!body?.organizationId){
            throw new BadRequestException("Organization Id is Required");

        }
        
        const matchStage: any = {
            organizationId: new Types.ObjectId(body?.organizationId),
            isActive: true,
            isSellOnline:true

        };
    
        if (body.search) {
            matchStage.name = { $regex: body.search, $options: "i" };
        }
    
        if (body?.fetchWithBundled === false) {
            matchStage.isBundledPricing = { $ne: true };
        }
    
        if (body?.classType) {
            matchStage['services.type'] = body.classType;
        }

        if (body?.fetchMemberships===true) {
            matchStage["membershipId"] = {$exists:true};
        }
    
        const aggregationPipeline: any[] = [
            { $match: matchStage },
            { $sort: { createdAt: -1 } },
            { $skip: skip },
            { $limit: pageSize },
            {
                $lookup: {
                    from: 'services',
                    localField: 'services.serviceCategory',
                    foreignField: '_id',
                    as: 'serviceCategoryDetails'
                }
            },
            {
                $addFields: {
                    serviceCategoryName: {
                        $arrayElemAt: ["$serviceCategoryDetails.name", 0]
                    }
                }
            },
            {
                $project: {
                    serviceCategoryDetails: 0 // remove unwanted joined field
                }
            }
        ];
    
        const [list, count] = await Promise.all([
            this.PricingModel.aggregate(aggregationPipeline),
            this.PricingModel.countDocuments(matchStage)
        ]);
    
        if (body?.fetchWithBundled === true && list?.length > 0) {
            for (const element of list) {
                let price = Number(element.price);
                if (element?.discount?.type === DiscountType.PERCENTAGE) {
                    element["discountedValue"] = Number(((element.discount.value / 100) * price).toFixed(2));
                } else if (element?.discount?.type === DiscountType.FLAT) {
                    element["discountedValue"] = Number((element?.discount?.value ? element?.discount?.value : 0).toFixed(2));
                } else {
                    element["discountedValue"] = 0.00;
                }
    
                if (element.isBundledPricing) {
                    const bundledPricing = await this.PricingModel.find({ _id: { $in: element.pricingIds } })
                        .select("services.type")
                        .lean();
                    element["type"] = [
                        ...new Set(bundledPricing.flatMap(bp => bp.services?.type ?? []))
                    ].join(", ");
                }
            }
        }
    
        return {
            list,
            count,
        };
    }
    



    async pricingStatusUpdate(updatePricingDto: ChangeStatusDto, user: IUserDocument, packageId: string): Promise<any> {
        const {role} = user
        let organizationId: any;
        if (role.type === ENUM_ROLE_TYPE.WEB_MASTER || role.type === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || role.type === ENUM_ROLE_TYPE.TRAINER) {
            const orgDetails = await this.StaffModel.findOne(
                { userId: user._id },
                { organizationId: 1 }
            ).lean();

            if (!orgDetails) {
                throw new NotFoundException(`Staff details not found`);
            }
            organizationId = orgDetails.organizationId;
        } else if (role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            organizationId = user._id;
        } else {
            throw new BadRequestException(`Access Denied: Invalid user role`);
        }
        const pricingModel = await this.PricingModel.findOne({
            _id: packageId,
            organizationId: organizationId,
        });

        if (!pricingModel) {
            throw new NotFoundException(`Pricing not found`);
        }

        if (pricingModel.isActive === updatePricingDto.status) {
            throw new BadRequestException(
                `Pricing is already ${updatePricingDto.status ? 'Active' : 'Inactive'}`
            );
        }

        pricingModel.isActive = updatePricingDto.status;
        await pricingModel.save();

        return {
            message: `Pricing status updated to ${updatePricingDto.status ? 'Active' : 'Inactive'}`,
            pricingModel,
        };
    }

    async createBundledPricingByOrg(createPricingDto: CreateBundledPricingDto, organizationId: string): Promise<any> {
        try {
            const pricingIds = createPricingDto.pricingIds.map(id => new Types.ObjectId(id));

            if (createPricingDto?.discount) {
                if (createPricingDto.discount.type === DiscountType.PERCENTAGE && (createPricingDto.discount.value < 0 || createPricingDto.discount.value > 100)) {
                    throw new BadRequestException("Invalid discount value");
                } else if (createPricingDto.discount.type === DiscountType.FLAT && createPricingDto.discount.value < 0) {
                    throw new BadRequestException("Invalid discount value");
                }
            }

            let data = {
                createdBy: organizationId,
                organizationId: organizationId,
                name: createPricingDto.name,
                price: createPricingDto.price,
                isSellOnline: createPricingDto.isSellOnline,
                tax: createPricingDto.tax,
                expiredInDays: createPricingDto.expiredInDays,
                durationUnit: createPricingDto.durationUnit,
                hsnOrSacCode: createPricingDto.hsnOrSacCode,
                discount: createPricingDto?.discount,
                pricingIds: pricingIds,
                isActive: true,
                isBundledPricing: true,
            }
            const addPricing = new this.PricingModel(data);
            return await addPricing.save();
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async createBundledPricingByStaff(createPricingDto: CreateBundledPricingDto, staffId: string): Promise<any> {
        try {
            const staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
            if (!staffDetails) {
                throw new BadRequestException("Staff not found");
            }

            const orgId = staffDetails.organizationId;
            const pricingIds = createPricingDto.pricingIds.map(id => new Types.ObjectId(id));


            if (createPricingDto?.discount) {
                if (createPricingDto.discount.type === DiscountType.PERCENTAGE && (createPricingDto.discount.value < 0 || createPricingDto.discount.value > 100)) {
                    throw new BadRequestException("Invalid discount value");
                } else if (createPricingDto.discount.type === DiscountType.FLAT && createPricingDto.discount.value < 0) {
                    throw new BadRequestException("Invalid discount value");
                }
            }

            let data = {
                createdBy: staffId,
                organizationId: orgId,
                name: createPricingDto.name,
                price: createPricingDto.price,
                isSellOnline: createPricingDto.isSellOnline,
                tax: createPricingDto.tax,
                expiredInDays: createPricingDto.expiredInDays,
                durationUnit: createPricingDto.durationUnit,
                hsnOrSacCode: createPricingDto.hsnOrSacCode,
                discount: createPricingDto?.discount,
                pricingIds: pricingIds,
                isActive: true,
                isBundledPricing: true,
            }
            const addPricing = new this.PricingModel(data);
            return await addPricing.save();
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    async bundledPricingListByOrg(pricingListDto: PricingListDto, orgId: string): Promise<any> {
        const pageSize = pricingListDto.pageSize ?? 10;
        const page = pricingListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let countProm = this.PricingModel.countDocuments({ organizationId: orgId, isBundledPricing: true });
        let listProm = this.PricingModel.aggregate([
            {
                $match: {
                    isBundledPricing: true,
                    organizationId: new Types.ObjectId(orgId),
                    ...(pricingListDto.search
                        ? { name: { $regex: pricingListDto.search, $options: "i" } }
                        : {})
                }
            },
            {
                $sort: { createdAt: -1 }
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "pricingIds",
                    foreignField: "_id",
                    as: "bundledPricingDetails"
                }
            },
            {
                $unwind: {
                    path: "$bundledPricingDetails",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "services",
                    let: {
                        serviceCategoryId: "$bundledPricingDetails.services.serviceCategory",
                        relationshipIds: {
                            $ifNull: ["$bundledPricingDetails.services.relationShip.serviceCategory", []]
                        }
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $or: [
                                        { $eq: ["$_id", "$$serviceCategoryId"] },
                                        { $in: ["$_id", "$$relationshipIds"] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 0,
                                name: 1
                            }
                        }
                    ],
                    as: "serviceCategories"
                }
            },
            {
                $group: {
                    _id: "$_id",
                    name: { $first: "$name" },
                    organizationId: { $first: "$organizationId" },
                    price: { $first: "$price" },
                    isSellOnline: { $first: "$isSellOnline" },
                    //tax: { $first: "$tax" },
                    //expiredInDays: { $first: "$expiredInDays" },
                    //hsnOrSacCode: { $first: "$hsnOrSacCode" },
                    //durationUnit: { $first: "$durationUnit" },
                    discount: { $first: "$discount" },
                    isActive: { $first: "$isActive" },
                    isBundledPricing: { $first: "$isBundledPricing" },
                    createdAt: { $first: "$createdAt" },
                    updatedAt: { $first: "$updatedAt" },
                    classType: { $addToSet: "$bundledPricingDetails.services.type" },
                    serviceCategoryNames: { $push: "$serviceCategories.name" }
                }
            },
            {
                $addFields: {
                    serviceCategoryNames: {
                        $reduce: {
                            input: {
                                $filter: {
                                    input: "$serviceCategoryNames",
                                    as: "item",
                                    cond: { $gt: [{ $size: "$$item" }, 0] }
                                }
                            },
                            initialValue: [],
                            in: { $setUnion: ["$$value", "$$this"] }
                        }
                    }
                }
            },
            { $sort: { updatedAt: -1 } },
            { $skip: skip },
            { $limit: pageSize }
        ])

        let [list, count] = await Promise.all([listProm, countProm]);
        if (list?.length > 0) {
            list.map((element) => {
                const price = Number(element?.price);
                element["discountedValue"] = price
                if (element?.discount?.type === DiscountType.PERCENTAGE) {
                    element["discountedValue"] = price - Number(((element?.discount?.value / 100) * price).toFixed(2));
                } else if (element?.discount?.type === DiscountType.FLAT) {
                    element["discountedValue"] = price - Number((element?.discount?.value ? element?.discount?.value : 0).toFixed(2));
                }
            });
        }
        return {
            list: list,
            count: count,
        };
    }

    async bundledPricingListByStaff(pricingListDto: PricingListDto, staffId: string): Promise<any> {
        const pageSize = pricingListDto.pageSize ?? 10;
        const page = pricingListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        let orgId = staffDetails["organizationId"];

        let countProm = this.PricingModel.countDocuments({ organizationId: orgId, isBundledPricing: true });
        let listProm = this.PricingModel.aggregate([
            {
                $match: {
                    isBundledPricing: true,
                    organizationId: new Types.ObjectId(orgId),
                    ...(pricingListDto.search
                        ? { name: { $regex: pricingListDto.search, $options: "i" } }
                        : {})
                }
            },
            {
                $sort: { createdAt: -1 }
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "pricingIds",
                    foreignField: "_id",
                    as: "bundledPricingDetails"
                }
            },
            {
                $unwind: {
                    path: "$bundledPricingDetails",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "services",
                    let: {
                        serviceCategoryId: "$bundledPricingDetails.services.serviceCategory",
                        relationshipIds: {
                            $ifNull: ["$bundledPricingDetails.services.relationShip.serviceCategory", []]
                        }
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $or: [
                                        { $eq: ["$_id", "$$serviceCategoryId"] },
                                        { $in: ["$_id", "$$relationshipIds"] }
                                    ]
                                }
                            }
                        },
                        {
                            $project: {
                                _id: 0,
                                name: 1
                            }
                        }
                    ],
                    as: "serviceCategories"
                }
            },
            {
                $group: {
                    _id: "$_id",
                    name: { $first: "$name" },
                    organizationId: { $first: "$organizationId" },
                    price: { $first: "$price" },
                    isSellOnline: { $first: "$isSellOnline" },
                    //tax: { $first: "$tax" },
                    //expiredInDays: { $first: "$expiredInDays" },
                    //hsnOrSacCode: { $first: "$hsnOrSacCode" },
                    //durationUnit: { $first: "$durationUnit" },
                    discount: { $first: "$discount" },
                    isActive: { $first: "$isActive" },
                    isBundledPricing: { $first: "$isBundledPricing" },
                    createdAt: { $first: "$createdAt" },
                    updatedAt: { $first: "$updatedAt" },
                    classType: { $addToSet: "$bundledPricingDetails.services.type" },
                    serviceCategoryNames: { $push: "$serviceCategories.name" }
                }
            },
            {
                $addFields: {
                    serviceCategoryNames: {
                        $reduce: {
                            input: {
                                $filter: {
                                    input: "$serviceCategoryNames",
                                    as: "item",
                                    cond: { $gt: [{ $size: "$$item" }, 0] }
                                }
                            },
                            initialValue: [],
                            in: { $setUnion: ["$$value", "$$this"] }
                        }
                    }
                }
            },
            { $skip: skip },
            { $limit: pageSize }
        ])

        let [list, count] = await Promise.all([listProm, countProm]);
        if (list?.length > 0) {
            list.map((element) => {
                const price = Number(element?.price);
                element["discountedValue"] = price
                if (element?.discount?.type === DiscountType.PERCENTAGE) {
                    element["discountedValue"] = price - Number(((element?.discount?.value / 100) * price).toFixed(2));
                } else if (element?.discount?.type === DiscountType.FLAT) {
                    element["discountedValue"] = price - Number((element?.discount?.value ? element?.discount?.value : 0).toFixed(2));
                }
            });
        }
        return {
            list: list,
            count: count,
        };
    }

    async updateBundledPricingByOrg(updatePricingDto: UpdateBundledPricingDto, orgId: string): Promise<any> {
        const checkPricing = await this.PricingModel.findOne({
            _id: updatePricingDto.pricingId,
            organizationId: orgId,
            isBundledPricing: true,
        });
        if (!checkPricing) {
            throw new BadRequestException("Pricing not found");
        }
        const pricingIds = updatePricingDto.pricingIds.map(id => new Types.ObjectId(id));

        if (updatePricingDto?.discount) {
            if (updatePricingDto.discount.type === DiscountType.PERCENTAGE && (updatePricingDto.discount.value < 0 || updatePricingDto.discount.value > 100)) {
                throw new BadRequestException("Invalid discount value");
            } else if (updatePricingDto.discount.type === DiscountType.FLAT && updatePricingDto.discount.value < 0) {
                throw new BadRequestException("Invalid discount value");
            }
        }

        const data = {
            name: updatePricingDto.name,
            price: updatePricingDto.price,
            isSellOnline: updatePricingDto.isSellOnline,
            tax: updatePricingDto.tax,
            expiredInDays: updatePricingDto.expiredInDays,
            durationUnit: updatePricingDto.durationUnit,
            hsnOrSacCode: updatePricingDto.hsnOrSacCode,
            discount: updatePricingDto?.discount,
            pricingIds: pricingIds,
            isActive: true,
            isBundledPricing: true,

        };
        const result = await this.PricingModel.findOneAndUpdate(
            { _id: updatePricingDto.pricingId },
            { $set: data },
            { new: true }
        );
        return result;
    }

    async updateBundledPricingByStaff(updatePricingDto: UpdateBundledPricingDto, staffId: string): Promise<any> {
        const staffDetails = await this.StaffModel.findOne({ userId: staffId }, { organizationId: 1 });
        if (!staffDetails) throw new BadRequestException("Staff not found");
        const orgId = staffDetails.organizationId;
        const checkPricing = await this.PricingModel.findOne({
            _id: updatePricingDto.pricingId,
            organizationId: orgId,
            isBundledPricing: true,
        });
        if (!checkPricing) throw new BadRequestException("Pricing not found");
        const pricingIds = updatePricingDto.pricingIds.map(id => new Types.ObjectId(id));
        if (updatePricingDto?.discount) {
            if (updatePricingDto.discount.type === DiscountType.PERCENTAGE && (updatePricingDto.discount.value < 0 || updatePricingDto.discount.value > 100)) {
                throw new BadRequestException("Invalid discount value");
            } else if (updatePricingDto.discount.type === DiscountType.FLAT && updatePricingDto.discount.value < 0) {
                throw new BadRequestException("Invalid discount value");
            }
        }

        const data = {
            name: updatePricingDto.name,
            price: updatePricingDto.price,
            isSellOnline: updatePricingDto.isSellOnline,
            tax: updatePricingDto.tax,
            expiredInDays: updatePricingDto.expiredInDays,
            durationUnit: updatePricingDto.durationUnit,
            hsnOrSacCode: updatePricingDto.hsnOrSacCode,
            discount: updatePricingDto?.discount,
            pricingIds: pricingIds,
            isActive: true,
            isBundledPricing: true,

        };
        const result = await this.PricingModel.findOneAndUpdate(
            { _id: updatePricingDto.pricingId },
            { $set: data },
            { new: true },
        );

        return result;
    }

    async pricingByUserAndSubType(reqBody: GetPricingByServiceSubtypeDto, user: IUserDocument): Promise<any> {
        let { classType, clientUserId, serviceCategoryId, subTypeId, search, pageSize, page } = reqBody;
        let role = user.role

        if (isNaN(page) || page < 1) {
            page = 1;
        }
        if (isNaN(pageSize) || pageSize < 1) {
            pageSize = 10;
        }
        const skip = (page - 1) * pageSize;

        let organizationId: any;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user?._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffModel.findOne({ userId: user.id }, { organizationId: 1 }).exec();
                if (!staffDetails) throw new BadRequestException("Not able to find Organization for this StaffMember");
                organizationId = staffDetails?.organizationId;
                break;
            case ENUM_ROLE_TYPE.USER:
                const clientDetails = await this.ClientModel.findOne({ userId: user.id }, { organizationId: 1 }).exec();
                if (!clientDetails) throw new BadRequestException("Not able to find Organization for this user");
                organizationId = clientDetails?.organizationId;
                break;
            default:
                throw new BadRequestException("Access Denied");
        }
        if (!organizationId) throw new BadRequestException("Organization ID not found");
        let matchCondition = [];
        if (reqBody.isNewBooking) {
            matchCondition.push({
                $match: {
                    $or: [
                        { remainingSession: { $gt: 0 } },
                        { remainingSession: "unlimited" }
                    ]
                }
            });
        }

        let purchasedPackages = await this.purchaseModel.aggregate([
            {
                $match: {
                    organizationId: new Types.ObjectId(organizationId),
                    userId: new Types.ObjectId(clientUserId),
                    isActive: true,
                    $and: [
                        { isExpired: { $ne: true } },
                        {
                            startDate: { $lte: new Date() },
                            endDate: { $gte: new Date() }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails"
                }
            },
            {
                $unwind: {
                    path: "$pricingDetails",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $match: {
                    "pricingDetails.services.type": classType,
                    $or: [
                        {
                            $and: [
                                {
                                    "pricingDetails.services.serviceCategory": new Types.ObjectId(serviceCategoryId)
                                },
                                {
                                    "pricingDetails.services.appointmentType": new Types.ObjectId(subTypeId)
                                }
                            ]
                        },
                        {
                            $and: [
                                {
                                    "pricingDetails.services.relationShip": {
                                        $elemMatch: {
                                            serviceCategory: new Types.ObjectId(serviceCategoryId),
                                            subTypeIds: new Types.ObjectId(subTypeId)
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            },
            ...(search?.length > 0 ? [{
                $match: {
                    "pricingDetails.name": { $regex: search, $options: "i" } // 🔍 Search implemented here
                }
            }] : []), {
                $set: {
                    sessionConsumed: { $ifNull: ["$sessionConsumed", 0] }
                }

            },
            {
                $addFields: {
                    remainingSession: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "unlimited",
                            else: { $subtract: ["$totalSessions", "$sessionConsumed"] }
                        }
                    },
                },
            },
            ...matchCondition,
            {
                $project: {
                    purchaseId: "$_id",
                    packageId: "$pricingDetails._id",
                    packageName: "$pricingDetails.name",
                    sessionCount: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "unlimited",
                            else: "$totalSessions"
                        }
                    },
                    sessionConsumed: "$sessionConsumed",
                    remainingSession: 1,
                    isPurchased: { $literal: true },
                    sessionType: 1,
                    dayPassLimit: 1


                }
            },
            {
                $skip: skip
            },
            {
                $limit: pageSize
            }
        ]).exec();

        if (role.type === ENUM_ROLE_TYPE.USER && purchasedPackages.length === 0) {
            purchasedPackages = await this.PricingModel.aggregate([
                {
                    $match: {
                        organizationId: new Types.ObjectId(organizationId),
                        "services.type": classType,
                        isActive: { $ne: false },
                        $or: [
                            {
                                "services.serviceCategory": new Types.ObjectId(serviceCategoryId),
                                "services.appointmentType": new Types.ObjectId(subTypeId)
                            },
                            {
                                "services.relationShip": {
                                    $elemMatch: {
                                        serviceCategory: new Types.ObjectId(serviceCategoryId),
                                        subTypeIds: new Types.ObjectId(subTypeId)
                                    }
                                }
                            }
                        ]
                    }
                },
                ...(search?.length > 0 ? [{
                    $match: {
                        name: { $regex: search, $options: "i" }
                    }
                }] : []),
                {
                    $project: {
                        packageId: "$_id",
                        packageName: "$name",
                        sessionCount: {
                            $cond: {
                                if: { $eq: ["$services.sessionCount", Infinity] },
                                then: "unlimited",
                                else: "$services.sessionCount"
                            }
                        },
                        sessionConsumed: { $literal: 0 },
                        remainingSession: {
                            $cond: {
                                if: { $eq: ["$services.sessionCount", Infinity] },
                                then: "unlimited",
                                else: "$services.sessionCount"
                            }
                        },
                        isPurchased: { $literal: false },
                        sessionType: 1,
                        dayPassLimit: 1

                    }
                },
                {
                    $skip: skip
                },
                {
                    $limit: pageSize
                }
            ]).exec();
        }
                if (purchasedPackages.length) {
                    const dayPassPurchases = purchasedPackages.length ? purchasedPackages.filter(item => item.sessionType === SessionType.DAY_PASS) : [];
                    const dayPassPurchaseIds = dayPassPurchases.map(item => new Types.ObjectId(item._id));
        
                    const sessionsCount = await this.SchedulingModel.aggregate([
                        {
                            $match: {
                                purchaseId: { $in: dayPassPurchaseIds },
                                scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                            },
                        },
                        {
                            $group: {
                                _id: { purchaseId: "$purchaseId", date: "$date" },
                            },
                        },
                    ]);
        
                    const sessionsByPurchaseId = sessionsCount.reduce((acc, session) => {
                        const purchaseId = session._id.purchaseId.toString();
                        const date = session._id.date.toISOString().split("T")[0];
                        if (!acc[purchaseId]) acc[purchaseId] = [];
                        acc[purchaseId].push(date);
                        return acc;
                    }, {});
        
                    const todayUTC = new Date();
                    todayUTC.setUTCHours(0, 0, 0, 0);
                    const todayISTDate = todayUTC.toISOString().split("T")[0];
        
                    for (const item of purchasedPackages || []) {
                        if (item.sessionType === SessionType.DAY_PASS) {
                            console.log("todayISTDate",todayISTDate)
                            console.log("sessionsByPurchaseId",sessionsByPurchaseId)
                            const bookedDates = sessionsByPurchaseId[item._id.toString()] || [];
                            const remainingDays = bookedDates.filter((date) => date < todayISTDate);
                            console.log("remainingDays",remainingDays)

                            const consumedDayPassLimit = remainingDays.length;
                            const assignedDayPassLimit = item.dayPassLimit || 0;
                            item.remainingSession = !isNaN(assignedDayPassLimit - consumedDayPassLimit) ? `${assignedDayPassLimit - consumedDayPassLimit} x Day Pass(es)` : 0;
                        }
                    }
        
                }

        return purchasedPackages;
    }

    async getServicesListByPackageIdV1(reqBody: GetServicesByPackageDTO): Promise<any> {
        let { search, pricingId } = reqBody
        let searchQuery = [];
        if (search?.length > 0) {
            searchQuery.push(
                { "serviceCategoryData.name": { $regex: search, $options: "i" } },
                { "serviceCategoryData.appointmentType.name": { $regex: search, $options: "i" } }
            );
        }
        const servicesList = await this.PricingModel.aggregate([
            {
                $match: {
                    _id: new Types.ObjectId(pricingId),
                },
            },
            {
                $lookup: {
                    from: "services",
                    localField: "services.serviceCategory",
                    foreignField: "_id",
                    as: "serviceCategoryData"
                }
            },
            {
                $unwind: {
                    path: "$serviceCategoryData",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    "serviceCategoryData.appointmentType": {
                        $filter: {
                            input: "$serviceCategoryData.appointmentType",
                            as: "apptType",
                            cond: {
                                $and: [
                                    { $in: ["$$apptType._id", "$services.appointmentType"] },
                                    { $ne: ["$$apptType.isActive", false] }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $match: searchQuery.length > 0 ? { $or: searchQuery } : {}
            },
            {
                $addFields: {
                    "services.relationShip": { $ifNull: ["$services.relationShip", []] }
                }
            },
            {
                $unwind: {
                    path: "$services.relationShip",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: "services",
                    localField: "services.relationShip.serviceCategory",
                    foreignField: "_id",
                    as: "relationshipServiceData"
                }
            },
            {
                $unwind: {
                    path: "$relationshipServiceData",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $addFields: {
                    "relationshipServiceData.subTypeIds": {
                        $filter: {
                            input: "$relationshipServiceData.appointmentType",
                            as: "subType",
                            cond: {
                                $and: [
                                    { $in: ["$$subType._id", "$services.relationShip.subTypeIds"] },
                                    { $ne: ["$$subType.isActive", false] }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $group: {
                    _id: "$_id",
                    services: {
                        $push: {
                            _id: "$serviceCategoryData._id",
                            name: "$serviceCategoryData.name",
                            appointmentType: { $ifNull: ["$serviceCategoryData.appointmentType", []] }
                        }
                    },
                    relationshipServices: {
                        $push: {
                            _id: "$relationshipServiceData._id",
                            name: "$relationshipServiceData.name",
                            appointmentType: { $ifNull: ["$relationshipServiceData.subTypeIds", []] }
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 1,
                    services: {
                        $concatArrays: [
                            "$services",
                            {
                                $filter: {
                                    input: "$relationshipServices",
                                    as: "relService",
                                    cond: {
                                        $gt: [{ $size: { $ifNull: ["$$relService.appointmentType", []] } }, 0]
                                    }
                                }
                            }
                        ]
                    }
                }
            },
        ]);

        return servicesList.length > 0 ? servicesList[0].services : [];
    }

}

