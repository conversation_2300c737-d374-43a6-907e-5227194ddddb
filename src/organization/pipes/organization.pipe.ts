import { Injectable } from "@nestjs/common";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { OrganizationDetailsDto } from "../dto/organization-details.dto";
import { Types } from "mongoose";

@Injectable()
export class OrganizationPipe {
    organizationList( organizationRoleId: any, skip: number, pageSize: number, search: string, filters: Object) {
        let pipeline = [
            {
                $match: {
                    role: new Types.ObjectId(organizationRoleId),
                    $or: [
                        {
                            name: {
                                $regex: `.*${search}.*`,
                                $options: "i",
                            },
                        },
                        {
                            email: {
                                $regex: `.*${search}.*`,
                                $options: "i",
                            },
                        },
                        {
                            mobile: {
                                $regex: `.*${search}.*`,
                                $options: "i",
                            },
                        },
                    ],
                },
            },
            {
                $sort: {
                    createdAt: -1,
                },
            },
            {
                $lookup: {
                    from: "organizations",
                    localField: "_id",
                    foreignField: "userId",
                    as: "organizationDetails",
                },
            },
            {
                $unwind: {
                    path: "$organizationDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $match: filters,
            },
            {
                $addFields: {
                    isPasswordReset: { $cond: { if: { $ifNull: ["$password", false] }, then: true, else: false } },
                },
            },
            {
                $facet: {
                    list: [
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                        {
                            $lookup: {
                                from: "cities",
                                localField: "organizationDetails.address.city",
                                foreignField: "_id",
                                as: "cityDetails",
                            },
                        },
                        {
                            $unwind: {
                                path: "$cityDetails",
                                preserveNullAndEmptyArrays: false,
                            },
                        },
                        {
                            $lookup: {
                                from: "facilities",
                                localField: "_id",
                                foreignField: "organizationId",
                                as: "facilities",
                            },
                        },
                        {
                            $project: {
                                organizationName: "$name",
                                image: "$organizationDetails.logo",
                                email: 1,
                                mobile: 1,
                                numberOfFacilities: { $size: "$facilities" },
                                city: "$cityDetails.name",
                                status: "$isActive",
                                isPasswordReset: 1,
                            },
                        },
                    ],
                    total: [
                        {
                            $count: "total",
                        },
                    ],
                },
            },
        ] as any;
        return pipeline;
    }

    organizationDetails(organizationDetailsDto: OrganizationDetailsDto) {
        let pipeline = [
            {
                $match: {
                    _id: Types.ObjectId.createFromHexString(organizationDetailsDto.organizationId),
                },
            },
            {
                $lookup: {
                    from: "organizations",
                    localField: "_id",
                    foreignField: "userId",
                    as: "organizationDetails",
                },
            },
            {
                $unwind: {
                    path: "$organizationDetails",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    name: 1,
                    firstName: 1,
                    lastName: 1,
                    mobile: 1,
                    email: 1,
                    status: "$isActive",
                    address: "$organizationDetails.address",
                    associatedPerson: "$organizationDetails.associatedPerson",
                    logo: "$organizationDetails.logo",
                    userOnboarding: "$organizationDetails.userOnboarding",
                    gymAttributes: "$organizationDetails.gymAttributes",
                    clientOnboarding: "$organizationDetails.clientOnboarding",
                    staffOnboarding: "$organizationDetails.staffOnboarding",
                },
            },
        ] as any;
        return pipeline;
    }

    // pricingListByServiceOrg(organizationId: string) {
    //     let pipeline = [
    //         {
    //             $project: {
    //                 name: 1,
    //                 organizationId: 1,
    //             },
    //         },
    //         {
    //             $match: {
    //                 organizationId: organizationId,
    //             },
    //         },
    //         {
    //             $lookup: {
    //                 from: "servicecategorypricings",
    //                 localField: "_id",
    //                 foreignField: "pricingId",
    //                 as: "pricing",
    //             },
    //         },
    //         {
    //             $addFields: {
    //                 alreadyExist: {
    //                     $cond: {
    //                         if: {
    //                             $gt: [
    //                                 {
    //                                     $size: "$pricing",
    //                                 },
    //                                 0,
    //                             ],
    //                         },
    //                         then: true,
    //                         else: false,
    //                     },
    //                 },
    //             },
    //         },
    //     ];
    //     return pipeline;
    // }

    // pricingListByServiceStaff(organizationId: string) {
    //     let pipeline = [
    //         {
    //             $project: {
    //                 name: 1,
    //                 organizationId: 1,
    //             },
    //         },
    //         {
    //             $match: {
    //                 organizationId: Types.ObjectId.createFromHexString(organizationId),
    //             },
    //         },
    //         {
    //             $lookup: {
    //                 from: "servicecategorypricings",
    //                 localField: "_id",
    //                 foreignField: "pricingId",
    //                 as: "pricing",
    //             },
    //         },
    //         {
    //             $addFields: {
    //                 alreadyExist: {
    //                     $cond: {
    //                         if: {
    //                             $gt: [
    //                                 {
    //                                     $size: "$pricing",
    //                                 },
    //                                 0,
    //                             ],
    //                         },
    //                         then: true,
    //                         else: false,
    //                     },
    //                 },
    //             },
    //         },
    //     ];
    //     return pipeline;
    // }
}



