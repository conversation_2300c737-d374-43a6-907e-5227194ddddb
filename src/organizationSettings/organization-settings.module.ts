import { Module } from '@nestjs/common';
import { OrganizationSettingsController } from './controllers/organization-settings.controller';
import { OrganizationSettingService } from './services/organization-settings.service';
import { MongooseModule } from '@nestjs/mongoose';
import { OrganizationSettings, OrganizationSettingsSchema } from './schemas/organization-settings.schema';
import { OrganizationSubSettings, OrganizationSubSettingsSchema } from './schemas/organization-sub-settings.schema';
import { SettingsOptions } from 'src/settingsOptions/schemas/setting-options.schema';
import { SettingsOptionsSchema } from 'src/settingsOptions/schemas/setting-options.schema';
import { AuthModule } from 'src/auth/auth.module';
import { SettingsOptionsModule } from 'src/settingsOptions/setting-options.module';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';

@Module({
  imports: [
    AuthModule,
    SettingsOptionsModule,
    MongooseModule.forFeature([
      { name: OrganizationSettings.name, schema: OrganizationSettingsSchema },
      { name: OrganizationSubSettings.name, schema: OrganizationSubSettingsSchema },
      { name: SettingsOptions.name, schema: SettingsOptionsSchema }
    ], DATABASE_PRIMARY_CONNECTION_NAME)
  ],
  controllers: [OrganizationSettingsController],
  providers: [OrganizationSettingService],
  exports: []
})
export class OrganizationSettingsModule {}
