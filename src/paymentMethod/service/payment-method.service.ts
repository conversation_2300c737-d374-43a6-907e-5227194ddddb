import { Injectable, BadRequestException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage } from "mongoose";
import { PaymentMethod } from "../schemas/payment-method.schema";
import { AddedPaymentMethodListDto, AddPaymentMethodDto, CreatePaymentMethodDto, PaymentMethodListDto, UpdatePaymentMethodStatusDto } from "../dto/payment-method.dto";
import { TransactionService } from "src/utils/services/transaction.service";
import { Facility } from "src/facility/schemas/facility.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { Invoice } from "src/users/schemas/invoice.schema";

@Injectable()
export class PaymentMethodService {
    constructor(
        @InjectModel(PaymentMethod.name) private readonly paymentMethodModel: Model<PaymentMethod>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Invoice.name) private InvoiceModel: Model<Invoice>,
        private readonly transactionService: TransactionService, // Transaction service for managing transactions
    ) {}

    /**
     * Create a new payment method
     * @param createPaymentMethodDto - Data to create the payment method
     * @returns The created payment method
     */
    async createPaymentMethod(createPaymentMethodDto: CreatePaymentMethodDto, user: any): Promise<{ message: string; data: PaymentMethod }> {
        try {
            function getUniqueId() {
                const chars = "abcdefghijklmnopqrstuvwxyz";
                const randomLetters = Array.from({ length: 3 }, () => chars[(Math.random() * 26) | 0]).join("");
                const randomNumbers = Math.random().toString().slice(2, 4); // Get two random digits
                const timestamp = Date.now().toString(36); // Base36 timestamp for uniqueness
                return randomLetters + randomNumbers + timestamp;
            }
            const shortId = getUniqueId();
            const paymentMethod = new this.paymentMethodModel({ ...createPaymentMethodDto, shortId });
            await paymentMethod.save();
            return {
                message: "Payment method created successfully",
                data: paymentMethod,
            };
        } catch (error) {
            console.error("Error creating payment method:", error.message); // Error log
            throw new BadRequestException("Failed to create payment method");
        }
    }

    /**
     * Add a new payment method
     * @param addPaymentMethodDto - Data to create the payment method
     * @returns The created payment method
     */
    async addPaymentMethod(addPaymentMethodDto: AddPaymentMethodDto, user: { _id: string }): Promise<{ message: string; data: PaymentMethod[] }> {
        try {
            const paymentMethod = await this.paymentMethodModel.findById(addPaymentMethodDto.paymentMethodId).lean();
            if (!paymentMethod) throw new NotFoundException("Payment method not found");
            const facility = await this.FacilityModel.findById(addPaymentMethodDto.facilityId);
            if (!facility) throw new NotFoundException("Facility not found");
            const isAlreadyAdded = facility.paymentMethods.some((pm) => pm.paymentMethodId.toString() === paymentMethod._id.toString());

            if (isAlreadyAdded) {
                throw new BadRequestException("Payment method already exists for this facility");
            }

            // Add payment method to facility
            facility.paymentMethods.push({
                name: paymentMethod.name,
                paymentMethodId: paymentMethod._id,
                addedBy: user._id,
                imageUrl: paymentMethod.imageUrl,
                shortId: paymentMethod.shortId,
            });

            await facility.save();
            return {
                message: "Payment method added successfully",
                data: facility.paymentMethods as any,
            };
        } catch (error) {
            console.error("Error adding payment method:", error.message);
            throw new BadRequestException("Failed to add payment method");
        }
    }

    /**
     * Remove a  payment method
     * @param addPaymentMethodDto - Data to create the payment method
     * @returns The Remove payment method
     */
    async removePaymentMethod(addPaymentMethodDto: AddPaymentMethodDto): Promise<{ message: string; data: PaymentMethod[] }> {
        try {
            const { facilityId, paymentMethodId } = addPaymentMethodDto;
            const facility = await this.FacilityModel.findById(facilityId);
            if (!facility) throw new NotFoundException("Facility not found");
            const invoiceData = await this.InvoiceModel.findOne({
                facilityId,
                "paymentDetails.paymentMethodId": paymentMethodId,
            }).lean();
            if (invoiceData) throw new NotFoundException("Payment method is already used and cannot be deleted.");
            const paymentMethods = facility.paymentMethods.filter((pm) => pm.paymentMethodId.toString() !== paymentMethodId.toString());
            facility.paymentMethods = paymentMethods;
            await facility.save();
            return {
                message: "Payment method removed successfully",
                data: facility.paymentMethods as any,
            };
        } catch (error) {
            console.error("Error in removing payment method:", error.message);
            throw new BadRequestException("Failed to remove payment method");
        }
    }

    /**
     * list all the payment method
     * @returns list all the payment method
     */

    async paymentMethodList(paymentMethodListDto: PaymentMethodListDto, user: any): Promise<any> {
        try {
            const { pageSize = 10, page = 1, search = "", facilityId, isActive } = paymentMethodListDto;
            const skip = pageSize * (page - 1);
            const matchStage: Record<string, any> = { isDeleted: false };

            if (facilityId) {
                const facility = await this.FacilityModel.findOne({ _id: facilityId }, "paymentMethods").lean();
                if (!facility) throw new NotFoundException("Facility not found");

                const usedPaymentMethodIds = facility.paymentMethods.map((pm) => pm.paymentMethodId);
                if (usedPaymentMethodIds.length > 0) {
                    matchStage._id = { $nin: usedPaymentMethodIds };
                }
            }

            if (typeof isActive === "boolean") matchStage.isActive = isActive;
            if (search) {
                matchStage.$or = [{ name: { $regex: search, $options: "i" } }];
            }

            const aggregationPipeline: PipelineStage[] = [
                { $match: matchStage },
                { $sort: { updatedAt: -1 } },
                { $skip: skip },
                { $limit: pageSize },
                {
                    $facet: {
                        data: [
                            { $project: { _id: 1, name: 1, isActive: 1, updatedAt: 1 } }, // Include required fields
                        ],
                        totalCount: [{ $count: "count" }],
                    },
                },
                {
                    $project: {
                        data: 1,
                        totalCount: { $ifNull: [{ $arrayElemAt: ["$totalCount.count", 0] }, 0] },
                    },
                },
            ];

            const [result] = await this.paymentMethodModel.aggregate(aggregationPipeline).exec();
            return {
                list: result?.data || [],
                count: result?.totalCount || 0,
            };
        } catch (error) {
            console.error("Error in paymentMethodList aggregation:", error.message);
            throw new BadRequestException("Failed to fetch payment method");
        }
    }

    async addedPaymentMethodList(addedPaymentMethodList: AddedPaymentMethodListDto): Promise<any> {
        try {
            const { facilityId, isActive } = addedPaymentMethodList;
            const facility = await this.FacilityModel.findOne({ _id: facilityId });
            if (!facility) throw new NotFoundException("Facility not found");
            let paymentMethods = facility.paymentMethods || [];
            if (typeof isActive === "boolean") paymentMethods = paymentMethods.filter((pm) => pm.isActive === isActive);
            return { data: paymentMethods };
        } catch (error) {
            console.error("Error fetching payment methods:", error.message);
            throw new BadRequestException("Failed to fetch payment methods");
        }
    }

    async getPaymentMethodById(paymentMethodId: string, user: any): Promise<any> {
        try {
            const result = await this.paymentMethodModel.findOne({ _id: paymentMethodId }).lean();
            return result;
        } catch (error) {
            console.log(error);
        }
    }
    async paymentMethodUpdate(updatePaymentMethodDto: CreatePaymentMethodDto, paymentMethodId: string, user: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const updateData = await this.paymentMethodModel.findOneAndUpdate(
                { _id: paymentMethodId },
                { $set: { name: updatePaymentMethodDto.name, imageUrl: updatePaymentMethodDto.imageUrl || "" } },
                { new: true, session },
            );

            if (!updateData) throw new BadRequestException("Payment method not found or not updated");

            await this.FacilityModel.updateMany(
                { "paymentMethods.paymentMethodId": paymentMethodId },
                { $set: { "paymentMethods.$.name": updatePaymentMethodDto.name, "paymentMethods.$.imageUrl": updatePaymentMethodDto.imageUrl || "" } },
                { session },
            );

            await this.transactionService.commitTransaction(session);
            return { data: updateData };
        } catch (error) {
            await this.transactionService.abortTransaction(session);

            throw error;
        } finally {
            session.endSession();
        }
    }

    async deletePaymentMethod(paymentMethodId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const iPaymentMethodUsed = await this.FacilityModel.findOne({ "paymentMethods.paymentMethodId": paymentMethodId });

            if (iPaymentMethodUsed) {
                throw new NotFoundException("Payment method is already used and cannot be deleted.");
            }
            const deletedData = await this.paymentMethodModel.findByIdAndDelete(paymentMethodId).session(session);
            if (!deletedData) throw new NotFoundException(`Payment method with ID ${paymentMethodId} not found`);
            await this.transactionService.commitTransaction(session);
            return {
                message: "Payment method deleted successfully",
                data: deletedData,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    async updatePaymentMethodStatus(updatePaymentMethodStatusDto: UpdatePaymentMethodStatusDto, user: any) {
        const session = await this.transactionService.startTransaction();
        try {
            const { facilityId, paymentMethodId, isActive } = updatePaymentMethodStatusDto;
            if (ENUM_ROLE_TYPE.SUPER_ADMIN === user.role) {
                const paymentMethod = await this.paymentMethodModel.findById(paymentMethodId).lean();
                if (!paymentMethod) throw new NotFoundException("Payment method not found");
                await this.paymentMethodModel.updateOne({ _id: paymentMethodId }, { $set: { isActive: isActive || false } }, { session });
            } else {
                if (!facilityId) throw new NotFoundException("facilityId is required.");
                const facility = await this.FacilityModel.findById(facilityId);
                if (!facility) throw new NotFoundException("Facility not found");

                facility.paymentMethods = facility.paymentMethods.map((pm: any) =>
                    pm._id.toString() === paymentMethodId.toString() ? { ...pm, isActive: isActive || false } : pm,
                );

                await facility.save({ session });
            }

            await this.transactionService.commitTransaction(session);
            return {
                message: "Status updated successfully",
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            if (session) session.endSession();
        }
    }
}
