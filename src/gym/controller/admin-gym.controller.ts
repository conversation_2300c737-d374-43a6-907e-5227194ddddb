import { BadRequestException, Body, Controller, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GymService } from "../service/gym.service";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { AuthGuard } from "@nestjs/passport";
import { Roles } from "src/auth/decorators/roles.decorator";
import { CreateGymDto } from "../dto/create-gym-profile.dto";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { UpdateGymDto } from "../dto/update-gym.dto";
import { GetUser } from "src/auth/decorators/get-user.decorator";

@ApiTags("Admin-Gym")
@ApiBearerAuth()
@Controller("admin/gym")
export class AdminGymController {
    constructor(private gymService: GymService) {}

    @Post("/register")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Register a new Gym" })
    async registerGym(@Body() registerGymDto: CreateGymDto): Promise<any> {
        return await this.gymService.adminRegisterGym(registerGymDto);
    }

    @Get("/profile/:id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Fetch user profile details by Id" })
    async getUserDetailsById(@Param("id") id: string): Promise<any> {
        try {
            const user = await this.gymService.adminGetGymDetailsById(id);
            return user;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Post("/get-all-gym-list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Fetch all user list" })
    async getAllUserList(@Body() paginationDTO: PaginationDTO): Promise<any> {
        try {
            const user = await this.gymService.adminGetAllGymList(paginationDTO);
            return user;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Patch("/update")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Gym Update" })
    async gymUpdate(@GetUser() user, @Body() updateGymDto: UpdateGymDto): Promise<{ message: String; data: any }> {
        let role = user.role;
        let output = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.SUPER_ADMIN:
                if (!updateGymDto.userId) throw new BadRequestException("required valid gym details");
                output = await this.gymService.updateGymByAdmin(updateGymDto);
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                output = await this.gymService.updateGym(updateGymDto, user._id);
                break;
            default:
                throw new BadRequestException("Invalid role. Access denied");
        }

        return {
            message: "Gym update",
            data: output,
        };
    }
}
