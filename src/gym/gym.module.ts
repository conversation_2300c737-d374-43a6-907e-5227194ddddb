import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { ClientProfileDetails, ClientProfileSchema } from 'src/users/schemas/user-profile-details.schema';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { UtilsModule } from 'src/utils/utils.module';
import { GymController } from './controller/gym.controller';
import { AdminGymController } from './controller/admin-gym.controller';
import { GymService } from './service/gym.service';
import { GymProfileDetails, GymProfileSchema } from './schemas/gym-profile-details.schema';
import { MailModule } from 'src/mail/mail.module';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';

@Module({
    imports: [
      forwardRef(() => AuthModule), 
      UtilsModule, 
      MailModule,
      MongooseModule.forFeature([
        { name: User.name, schema: UserSchema },
        { name: ClientProfileDetails.name, schema: ClientProfileSchema },
        { name: GymProfileDetails.name, schema: GymProfileSchema }
      ], DATABASE_PRIMARY_CONNECTION_NAME)
    ],
    controllers: [GymController,AdminGymController],
    providers: [GymService],
    exports: [GymService, MongooseModule.forFeature([{ name: User.name, schema: UserSchema }], DATABASE_PRIMARY_CONNECTION_NAME)],
})
export class GymModule {}
