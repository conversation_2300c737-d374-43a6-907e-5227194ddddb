import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { TransactionService } from "src/utils/services/transaction.service";
import { AttributeDto } from "../dto/attribute.dto";
import { AttributeStatusDto } from "../dto/attribute-status.dto";
import { AttributeListDto } from "../dto/attribute-list.dto";
import { Attributes } from "../schema/attribute.schema";
import { AttributeListFilterDto } from "../dto/attribute-list-filter.dto";
import { Services } from "src/organization/schemas/services.schema";
import { AttributeType } from "src/utils/enums/attribute-type.enum";
import { UserDocument } from "src/users/schemas/user.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";

@Injectable()
export class AttributeService {
    constructor(
        @InjectModel(Attributes.name) private AttributeModel: Model<Attributes>,
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(StaffProfileDetails.name) private readonly StaffProfileModel: Model<StaffProfileDetails>,
        private readonly transactionService: TransactionService,
    ) {}

    private async getOrganizationId(user: IUserDocument) {
        const { role } = user;
        let organizationId = null;
        switch (role.type) {
            // case ENUM_ROLE_TYPE.USER:
            //     return user._id

            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.WEB_MASTER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break;

            default:
                throw new BadRequestException("Access denied");
        }
        if(!organizationId){
            throw new BadRequestException("Access denied")
        }
        return organizationId;
    }


    async createAttribute(createAttributeDto: AttributeDto, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        const session = await this.transactionService.startTransaction();
        try {
            const existingData = await this.AttributeModel.findOne({
                organizationId,
                attributeType: AttributeType.TIER,
                name: createAttributeDto.name,
            });
            if (existingData) throw new BadRequestException("Appointment Type already exists.");
            let attributeData = {
                organizationId: organizationId,
                attributeType: createAttributeDto.attributeType,
                name: createAttributeDto.name,
            };

            let data = new this.AttributeModel(attributeData);
            let resp = await data.save({ session });
            await this.transactionService.commitTransaction(session);
            return resp;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async attributeListing(attributeListDto: AttributeListDto, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        const session = await this.transactionService.startTransaction();
        try {
            const pageSize = attributeListDto.pageSize ?? 10;
            const page = attributeListDto.page ?? 1;
            const skip = pageSize * (page - 1);
            let query = {
                organizationId: organizationId,
            };

            if (attributeListDto.search) {
                const titleQueryString = attributeListDto.search.trim().split(" ").join("|");
                query["$or"] = [{ name: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
            }
            const total = await this.AttributeModel.countDocuments(query);
            const data = await this.AttributeModel.find(query).sort({ createdAt: -1 }).limit(pageSize).skip(skip).exec();
            await this.transactionService.commitTransaction(session);
            return {
                list: data,
                count: total,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async attributeUpdate(updateAttributeDto: AttributeDto, attributeId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        const session = await this.transactionService.startTransaction();
        try {
            const existingData = await this.AttributeModel.findOne({
                _id: { $ne: attributeId },
                organizationId,
                attributeType: AttributeType.TIER,
                name: updateAttributeDto.name,
            });
            if (existingData) throw new BadRequestException("Appointment Type already exists.");
            let attributeData = {
                attributeType: updateAttributeDto.attributeType,
                name: updateAttributeDto.name,
                image: updateAttributeDto.image,
            };
            let updateAttribute = await this.AttributeModel.findOneAndUpdate(
                {
                    _id: attributeId,
                    organizationId: organizationId,
                },
                {
                    $set: attributeData,
                },
                {
                    new: true,
                    session,
                },
            );

            if (!updateAttribute) throw new BadRequestException("Attribute not found");

            if (updateAttributeDto.attributeType === AttributeType.TIER)
                await this.ServiceModel.updateMany(
                    { "appointmentType._id": attributeId },
                    { $set: { "appointmentType.$[elem].name": updateAttributeDto.name } },
                    { arrayFilters: [{ "elem._id": attributeId }] },
                );

            await this.transactionService.commitTransaction(session);
            return updateAttribute;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async attributeStatusUpdate(updateAttributeStatusDto: AttributeStatusDto, attributeId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        try {
            let updateAttribute = await this.AttributeModel.findOneAndUpdate(
                {
                    _id: attributeId,
                    organizationId: organizationId,
                },
                {
                    $set: { isActive: updateAttributeStatusDto.isActive },
                },
                { new: true },
            );
            if (!updateAttribute) throw new BadRequestException("Attribute not found");
            if (updateAttributeStatusDto.attributeType === AttributeType.TIER)
                await this.ServiceModel.updateMany(
                    { "appointmentType._id": attributeId },
                    { $set: { "appointmentType.$[elem].isActive": updateAttributeStatusDto.isActive } },
                    { arrayFilters: [{ "elem._id": attributeId }] },
                );

            return updateAttribute;
        } catch (error) {
            throw error;
        }
    }

    async attributeDelete(attributeId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        const session = await this.transactionService.startTransaction();
        try {
            let attributeData = await this.AttributeModel.findOne({
                _id: attributeId,
                organizationId: organizationId,
            });
            if (!attributeData) throw new BadRequestException("Attribute not found");
            if (attributeData.attributeType === AttributeType.TIER) {
                let existingAppointmentType = await this.ServiceModel.findOne({ "appointmentType._id": attributeId });
                if (existingAppointmentType) throw new BadRequestException("Attribute is mapped with  service category.");
            }
            await this.AttributeModel.deleteOne(
                {
                    _id: attributeId,
                    organizationId: organizationId,
                },
                { session },
            );
            await this.transactionService.commitTransaction(session);
            return attributeData;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async attributeListByType(attributeFilterDto: AttributeListFilterDto, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        const session = await this.transactionService.startTransaction();
        try {
            let query = { attributeType: attributeFilterDto.attributeType, organizationId: organizationId };

            if (attributeFilterDto.search) {
                const titleQueryString = attributeFilterDto.search.trim();
                query["$or"] = [{ name: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
            }
            const data = await this.AttributeModel.find(query).sort({ updatedAt: -1 });
            await this.transactionService.commitTransaction(session);
            return data;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async attributeDetailsById(attributeId: string, user: any): Promise<any> {
        const organizationId = await this.getOrganizationId(user)
        let details = await this.AttributeModel.findOne({ _id: attributeId, organizationId: organizationId });
        if (!details) throw new BadRequestException("Invalid attribute details");
        return details;
    }
}
