import { Body, Controller, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { CustomPackageService } from "../services/custom-package.service";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { ENUM_ROLE_TYPE } from "src/policy/enums/policy.enum";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateCustomPackageDto, CustomPackageListDto, UpdateCustomPackageDto } from "src/customPackage/dto/custom-package.dto";

@ApiTags("custom-package")
@ApiBearerAuth()
@Controller("custom-package")
export class CustomPackageController {
    constructor(private customPackageService: CustomPackageService) {}

    @Post("/create")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @ApiOperation({ summary: "Create Custom Package" })
    async createCourseScheduling(@GetUser() user: any, @Body() createCustomPackageDto: CreateCustomPackageDto): Promise<any> {
        const output = await this.customPackageService.createCustomPackage(createCustomPackageDto, user);
        return output;
    }

    @Patch("/update")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @ApiOperation({ summary: "Update Custom Package" })
    async updateCourseScheduling(@GetUser() user: any, @Body() updateCustomPackageDto: UpdateCustomPackageDto): Promise<any> {
        const output = await this.customPackageService.updateCustomPackage(updateCustomPackageDto, user);
        return output;
    }

    @Post("/list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @ApiOperation({ summary: "Custom Package list" })
    async getSchedulingList(@Body() customPackageListDto: CustomPackageListDto): Promise<any> {
        const output = await this.customPackageService.getCustomPackageList(customPackageListDto);
        return output;
    }

    @Get("/details/:id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @ApiOperation({ summary: "Custom Package Details" })
    async getSchedulingDetails(@GetUser() user: any, @Param("id") id: string): Promise<any> {
        let output = await this.customPackageService.getCustomPackageDetails(user, id);
        return output;
    }
}
