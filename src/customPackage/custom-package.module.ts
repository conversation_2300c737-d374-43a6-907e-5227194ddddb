import { Module } from "@nestjs/common";
import { CustomPackageController } from "./controllers/custom-package.controller";
import { CustomPackageService } from "./services/custom-package.service";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { AuthModule } from "src/auth/auth.module";
import { MailModule } from "src/mail/mail.module";
import { Clients, ClientSchema } from "src/users/schemas/clients.schema";
import { WaitTimeModule } from "src/wait-time/wait-time.module";
import { CustomPackage, CustomPackageSchema } from "src/customPackage/schemas/custom-package.schema";

@Module({
    imports: [
        WaitTimeModule,
        UtilsModule,
        AuthModule,
        MailModule,
        MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: CustomPackage.name, schema: CustomPackageSchema },
            { name: Clients.name, schema: ClientSchema },
        ]),
    ],
    controllers: [CustomPackageController],
    providers: [CustomPackageService],
})
export class CustomPackageModule {}
