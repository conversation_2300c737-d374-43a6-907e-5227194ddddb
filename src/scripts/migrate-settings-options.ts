import { NestFactory } from '@nestjs/core';
import { CommandModule, CommandService } from 'nestjs-command';
import { MigrationModule } from 'src/migration/migration.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
    const app = await NestFactory.createApplicationContext(MigrationModule, {
        logger: ['error', 'warn', 'log'],
    });

    const logger = new Logger('Settings-Options-Migration');

    try {
        // Execute the seed:settings-options command
        await app
            .select(CommandModule)
            .get(CommandService)
            .exec('seed:settings-options');
        
        logger.log('Settings options migration completed successfully');
        process.exit(0);
    } catch (error) {
        logger.error('Error during settings options migration:', error);
        process.exit(1);
    }
}

bootstrap();
