import { NestFactory } from "@nestjs/core";
import { MongooseModule } from "@nestjs/mongoose";
import { Module, Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import dotenv from "dotenv";
import { PaymentMethod, PaymentMethodSchema } from "../paymentMethod/schemas/payment-method.schema";
import { Facility, FacilityDocument, FacilitySchema } from "src/facility/schemas/facility.schema";

dotenv.config();

// MongoDB Connection URL
// const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_URI = process.env.MONGODB_URI;
@Injectable()
class PaymentMethodService {
    constructor(@InjectModel(PaymentMethod.name) private paymentMethodModel: Model<PaymentMethod>, @InjectModel(Facility.name) private facilityModel: Model<FacilityDocument>) {}

    async insertPaymentMethods() {
        const paymentMethods = [
            { name: "Cash", shortId: "cash", isDefault: true },
            { name: "Split Payment", shortId: "splitPayment", isDefault: true },
        ];

        try {
            // Insert Payment Methods if they don't exist
            const insertedPayments = await Promise.all(
                paymentMethods.map(async (pm) => {
                    return this.paymentMethodModel.findOneAndUpdate({ shortId: pm.shortId }, pm, { upsert: true, new: true });
                }),
            );

            console.log("Payment Methods Inserted/Updated:", insertedPayments);

            // Extract IDs
            const paymentMethodIds = insertedPayments.map((pm) => ({
                paymentMethodId: pm._id,
                name: pm.name,
                shortId: pm.shortId,
                imageUrl: "",
                isActive: true,
                isDefault: true,
            }));

            // Find all facilities and update their paymentMethods array
            const facilities = await this.facilityModel.find({ billingDetails: { $exists: true } });
            for (const facility of facilities) {
                // Avoid duplicates
                const existingPaymentMethodIds = facility.paymentMethods.map((pm) => pm.paymentMethodId.toString());
                const newPaymentMethods: any = paymentMethodIds.filter((pm) => !existingPaymentMethodIds.includes(pm.paymentMethodId.toString()));

                if (newPaymentMethods.length > 0) {
                    facility.paymentMethods.push(...newPaymentMethods);
                    await facility.save();
                }
            }

            console.log("Payment Methods added to Facilities:", facilities);
        } catch (error) {
            console.error("Error inserting payment methods:", error);
        }
    }
}

@Module({
    imports: [
        MongooseModule.forRoot(MONGODB_URI),
        MongooseModule.forFeature([
            { name: PaymentMethod.name, schema: PaymentMethodSchema },
            { name: Facility.name, schema: FacilitySchema },
        ]),
    ],
    providers: [PaymentMethodService], // Register the service properly
})
class AppModule {}

async function bootstrap() {
    const app = await NestFactory.createApplicationContext(AppModule);
    const paymentMethodService = app.get(PaymentMethodService);
    await paymentMethodService.insertPaymentMethods();
    await app.close();
}

bootstrap();
