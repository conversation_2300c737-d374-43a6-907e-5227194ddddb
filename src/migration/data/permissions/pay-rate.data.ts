import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const payRatePermissions: PermissionCreateRequestDto[] = [
    {
        name: "Pay Rate Write",
        type: ENUM_PERMISSION_TYPE.PAY_RATE_WRITE,
        description: 'Grants pay rate write access',
        isDelegated: true,
    },
    {
        name: "Pay Rate Read",
        type: ENUM_PERMISSION_TYPE.PAY_RATE_READ,
        description: 'Grants pay rate read access',
        isDelegated: true,
    },
    {
        name: "Pay Rate Update",
        type: ENUM_PERMISSION_TYPE.PAY_RATE_UPDATE,
        description: 'Grants pay rate update access',
        isDelegated: true,
    },
    {
        name: "Pay Rate Delete",
        type: ENUM_PERMISSION_TYPE.PAY_RATE_DELETE,
        description: 'Grants pay rate delete access',
        isDelegated: true,
    },
];