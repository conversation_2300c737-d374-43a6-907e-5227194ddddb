import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const promotionsPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Promotion Write",
        type: ENUM_PERMISSION_TYPE.PROMOTION_WRITE,
        description: 'Grants promotion write access',
        isDelegated: true,
    },
    {
        name: "Promotion Read",
        type: ENUM_PERMISSION_TYPE.PROMOTION_READ,
        description: 'Grants promotion read access',
        isDelegated: true,
    },
    {
        name: "Promotion Update",
        type: ENUM_PERMISSION_TYPE.PROMOTION_UPDATE,
        description: 'Grants promotion update access',
        isDelegated: true,
    },
    {
        name: "Promotion Delete",
        type: ENUM_PERMISSION_TYPE.PROMOTION_DELETE,
        description: 'Grants promotion delete access',
        isDelegated: true,
    },
    {
        name: "Promotion Apply",
        type: ENUM_PERMISSION_TYPE.PROMOTION_APPLY,
        description: 'Grants permission to apply promotions to products/services',
        isDelegated: true,
    },
];
