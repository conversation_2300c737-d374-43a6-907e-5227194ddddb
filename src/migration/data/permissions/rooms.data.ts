import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const roomsPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Room Write",
        type: ENUM_PERMISSION_TYPE.ROOM_WRITE,
        description: 'Grants room write access',
        isDelegated: true,
    },
    {
        name: "Room Read",
        type: ENUM_PERMISSION_TYPE.ROOM_READ,
        description: 'Grants room read access',
        isDelegated: true,
    },
    {
        name: "Room Update",
        type: ENUM_PERMISSION_TYPE.ROOM_UPDATE,
        description: 'Grants room update access',
        isDelegated: true,
    },
    {
        name: "Room Delete",
        type: ENUM_PERMISSION_TYPE.ROOM_DELETE,
        description: 'Grants room delete access',
        isDelegated: true,
    },
];

