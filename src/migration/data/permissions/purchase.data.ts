import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const purchasePermissions: PermissionCreateRequestDto[] = [
    {
        name: "Purchase Write",
        type: ENUM_PERMISSION_TYPE.PURCHASE_WRITE,
        description: 'Grants purchase write access',
        isDelegated: true,
    },
    {
        name: "Purchase Read",
        type: ENUM_PERMISSION_TYPE.PURCHASE_READ,
        description: 'Grants purchase read access',
        isDelegated: true,
    },
    {
        name: "Purchase Update",
        type: ENUM_PERMISSION_TYPE.PURCHASE_UPDATE,
        description: 'Grants purchase update access',
        isDelegated: true,
    },
    {
        name: "Purchase Export",
        type: ENUM_PERMISSION_TYPE.PURCHASE_EXPORT,
        description: 'Grants purchase export access',
        isDelegated: true,
    },
    {
        name: "Purchase Report Export",
        type: ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT,
        description: 'Grants purchase report export access',
        isDelegated: true,
    },
    {
        name: "Purchase Invoice Read",
        type: ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_READ,
        description: 'Grants purchase invoice read access',
        isDelegated: true,
    },
    {
        name: "Purchase Invoice Write",
        type: ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_WRITE,
        description: 'Grants purchase invoice write access',
        isDelegated: true,
    },
];

