import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const pricingPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Pricing Write",
        type: ENUM_PERMISSION_TYPE.PRICING_WRITE,
        description: 'Grants pricing write access',
        isDelegated: true,
    },
    {
        name: "Pricing Read",
        type: ENUM_PERMISSION_TYPE.PRICING_READ,
        description: 'Grants pricing read access',
        isDelegated: true,
    },
    {
        name: "Pricing Update",
        type: ENUM_PERMISSION_TYPE.PRICING_UPDATE,
        description: 'Grants pricing update access',
        isDelegated: true,
    },
    {
        name: "Pricing Delete",
        type: ENUM_PERMISSION_TYPE.PRICING_DELETE,
        description: 'Grants pricing delete access',
        isDelegated: true,
    },
   
];

