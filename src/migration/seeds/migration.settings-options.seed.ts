import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { SettingsOptions } from 'src/settingsOptions/schemas/setting-options.schema';
import { SubSettingsOptions } from 'src/settingsOptions/schemas/sub-setting-options.schema';

@Injectable()
export class MigrationSettingsOptionsSeed {
    constructor(
        @InjectModel(SettingsOptions.name) private readonly settingsOptionsModel: Model<SettingsOptions>,
    ) {}

    @Command({
        command: 'seed:settings-options',
        describe: 'Seed settings options',
    })
    async seeds(): Promise<void> {
        try {
            // Define the settings data to be migrated
            const settingsData = [
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f3007"),
                    order: 0,
                    key: "settings_class_setup",
                    name: "Class Setup",
                    description: "Enable/disable assessment",
                    isActive: false,
                    default: true,
                    inOperation: true,
                    href: "",
                    subSettings: [
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f3008"),
                            order: 0,
                            key: "subsettings_class_setup_booking",
                            groupKey: "",
                            name: "Booking",
                            description: "Enable/disable measurement",
                            isActive: false,
                            default: false,
                            subSettings: [],
                            href: "",
                        },
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f3009"),
                            order: 0,
                            key: "subsettings_class_setup_personal_appontment",
                            groupKey: "",
                            name: "Personal Appointmnt",
                            description: "Enable/disable measurement",
                            isActive: false,
                            default: false,
                            subSettings: [],
                            href: "",
                        },
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f300a"),
                            order: 0,
                            key: "subsettings_class_setup_courses",
                            groupKey: "",
                            name: "Courses",
                            description: "Enable/disable measurement",
                            isActive: false,
                            default: false,
                            subSettings: [],
                            href: "",
                        },
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f300b"),
                            order: 0,
                            key: "subsettings_class_setup_classes",
                            groupKey: "",
                            name: "Classes",
                            description: "Enable/disable measurement",
                            isActive: false,
                            default: false,
                            subSettings: [],
                            href: "",
                        }
                    ],
                },
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f300c"),
                    order: 1,
                    key: "settings_class_scheduling",
                    name: "Class Scheduling",
                    description: "Enable/disable assessment",
                    isActive: false,
                    default: true,
                    inOperation: true,
                    href: "",
                    subSettings: [
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f300d"),
                            order: 0,
                            key: "subsettings_class_scheduling_sharepass",
                            groupKey: "",
                            name: "Share Pass",
                            description: "Enable/disable measurement",
                            isActive: false,
                            default: false,
                            subSettings: [],
                            href: "",
                        },
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f300e"),
                            order: 0,
                            key: "subsettings__class_scheduling_unpaid_class",
                            groupKey: "",
                            name: "Unpaid Class",
                            description: "Enable/disable measurement",
                            isActive: false,
                            default: false,
                            subSettings: [],
                            href: "",
                        }
                    ],
                },
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f300f"),
                    order: 2,
                    key: "settings_point_of_sale",
                    name: "Point of Sale",
                    description: "Enable/disable assessment",
                    isActive: false,
                    default: true,
                    inOperation: true,
                    href: "",
                    subSettings: [
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f3010"),
                            order: 0,
                            key: "subsettings_point_of_sale_pay_later",
                            groupKey: "",
                            name: "Pay Later",
                            description: "Enable/disable measurement",
                            isActive: false,
                            default: false,
                            subSettings: [],
                            href: "",
                        }
                    ],
                },
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f3011"),
                    order: 2,
                    key: "settings_pin",
                    name: "Pin System",
                    description: "Enable/disable pin authentication",
                    isActive: false,
                    default: false,
                    inOperation: true,
                    href: "",
                    subSettings: [],
                },
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f3012"),
                    order: 3,
                    key: "settings_feature_tabs",
                    name: "Feature Tabs",
                    description: "Enable/disable rooms",
                    isActive: false,
                    default: false,
                    inOperation: true,
                    href: "/setting/feature-tab",
                    subSettings: [],
                },
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f3013"),
                    order: 4,
                    key: "settings_amenities",
                    name: "Amenities",
                    description: "Enable/disable amenities",
                    isActive: false,
                    default: false,
                    inOperation: true,
                    href: "/amenities",
                    subSettings: [],
                },
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f3014"),
                    order: 5,
                    key: "settings_membership",
                    name: "Membership",
                    description: "Enable/disable membership",
                    isActive: false,
                    default: false,
                    inOperation: true,
                    href: "/setting/membership",
                    subSettings: [],
                },
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f3015"),
                    order: 6,
                    key: "settings_announcement",
                    name: "Announcement",
                    description: "Enable/disable announcement",
                    isActive: false,
                    default: false,
                    inOperation: true,
                    href: "/setting/announcements",
                    subSettings: [],
                },
                {
                    _id: new Types.ObjectId("67ea250f94c2721f117f3016"),
                    order: 0,
                    key: "settings_client_onboarding",
                    name: "Measurement",
                    description: "Enable/disable assessment",
                    isActive: false,
                    default: false,
                    inOperation: true,
                    href: "",
                    subSettings: [
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f3017"),
                            order: 0,
                            key: "subsettings_client_onboarding_assessment",
                            groupKey: "",
                            name: "Assessments",
                            description: "Enable/disable measurement",
                            isActive: false,
                            default: false,
                            subSettings: [
                                {
                                    _id: new Types.ObjectId("67ea250f94c2721f117f3018"),
                                    order: 0,
                                    key: "subsettings_client_onboarding_weight",
                                    groupKey: "subsettings_client_onboarding_assessment",
                                    name: "Measurement",
                                    description: "Enable/disable measurement",
                                    isActive: false,
                                    default: false,
                                    subSettings: [],
                                    href: "",
                                },
                                {
                                    _id: new Types.ObjectId("67ea250f94c2721f117f3019"),
                                    order: 0,
                                    key: "subsettings_client_onboarding_measurement",
                                    groupKey: "subsettings_client_onboarding_assessment",
                                    name: "Measurement",
                                    description: "Enable/disable measurement",
                                    isActive: false,
                                    default: false,
                                    subSettings: [],
                                    href: "",
                                }
                            ],
                            href: "",
                        },
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f301a"),
                            order: 0,
                            key: "subsettings_client_onboarding_policies",
                            groupKey: "",
                            name: "Policies",
                            description: "Enable/disable policy",
                            isActive: false,
                            default: false,
                            subSettings: [
                                {
                                    _id: new Types.ObjectId("67ea250f94c2721f117f301b"),
                                    order: 0,
                                    key: "subsettings_client_onboarding_facility_waiver",
                                    groupKey: "subsettings_client_onboarding_policies",
                                    name: "Facility waiver",
                                    description: "Enable/disable Facility waiver",
                                    isActive: true,
                                    default: false,
                                    subSettings: [],
                                    href: "",
                                },
                                {
                                    _id: new Types.ObjectId("67ea250f94c2721f117f301c"),
                                    order: 0,
                                    key: "subsettings_client_onboarding_safety_briefing",
                                    groupKey: "subsettings_client_onboarding_policies",
                                    name: "Safety Briefing Done",
                                    description: "Enable/disable Safety Briefing Done",
                                    isActive: true,
                                    default: false,
                                    subSettings: [],
                                    href: "",
                                },
                                {
                                    _id: new Types.ObjectId("67ea250f94c2721f117f301d"),
                                    order: 0,
                                    key: "subsettings_client_onboarding_check_id",
                                    groupKey: "subsettings_client_onboarding_policies",
                                    name: "Check For Id",
                                    description: "Enable/disable Safety Briefing Done",
                                    isActive: true,
                                    default: false,
                                    subSettings: [],
                                    href: "",
                                }
                            ],
                            href: "",
                        },
                        {
                            _id: new Types.ObjectId("67ea250f94c2721f117f301e"),
                            order: 0,
                            key: "subsettings_client_onboarding_notes",
                            groupKey: "",
                            name: "Notes",
                            description: "Enable/disable verification",
                            isActive: false,
                            default: false,
                            subSettings: [],
                            href: "",
                        }
                    ],
                }
            ];

            // Insert settings data
            for (const setting of settingsData) {
                // Check if setting already exists
                const existingSetting = await this.settingsOptionsModel.findOne({ key: setting.key });
                
                if (existingSetting) {
                    console.log(`Setting with key ${setting.key} already exists. Skipping...`);
                    continue;
                }
                
                // Create new setting
                await this.settingsOptionsModel.create(setting);
                console.log(`Setting with key ${setting.key} created successfully.`);
            }

            console.log('Settings options migration completed successfully.');
        } catch (err: any) {
            console.error('Error during settings options migration:', err);
            throw new Error(err);
        }

        return;
    }

    @Command({
        command: 'remove:settings-options',
        describe: 'Remove settings options',
    })
    async remove(): Promise<void> {
        try {
            await this.settingsOptionsModel.deleteMany({});
            console.log('All settings options removed successfully.');
        } catch (err: any) {
            console.error('Error removing settings options:', err);
            throw new Error(err);
        }

        return;
    }
}
