import { Module } from '@nestjs/common';
import { CommandModule } from 'nestjs-command';
import { CommonModule } from 'src/common/common.module';
import { RoleModule } from 'src/role/role.module';
import { MigrationRoleSeed } from './seeds/migration.role.seed';
import { MigrationPermissionSeed } from './seeds/migration.permissions.seed';
import { PolicyModule } from 'src/policy/policy.module';
import { MigrationPolicySeed } from './seeds/migration.policy.seed';
import { UsersModule } from 'src/users/users.module';
import { MigrationChangeRoleId } from './seeds/migration.change-role-id.seed';
import { UtilsModule } from 'src/utils/utils.module';
import { MigrationSettingsOptionsSeed } from './seeds/migration.settings-options.seed';
import { MongooseModule } from '@nestjs/mongoose';
import { SettingsOptions, SettingsOptionsSchema } from 'src/settingsOptions/schemas/setting-options.schema';

@Module({
    imports: [
        CommonModule,
        CommandModule,
        UtilsModule,
        RoleModule,
        PolicyModule,
        UsersModule,
        MongooseModule.forFeature([
            { name: SettingsOptions.name, schema: SettingsOptionsSchema }
        ])
    ],
    providers: [
        MigrationRoleSeed,
        MigrationPermissionSeed,
        MigrationPolicySeed,
        MigrationChangeRoleId,
        MigrationSettingsOptionsSeed,
    ],
    exports: [],
})
export class MigrationModule {}
