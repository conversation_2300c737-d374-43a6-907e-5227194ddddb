import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsMongoId, IsNotEmpty, IsOptional } from 'class-validator';
import { ENUM_ITEM_TYPE } from '../enums/item-type.enum';

export class ApplyPromotionDto {

  @ApiProperty({
    description: 'Type of items to apply the promotion to',
    enum: ENUM_ITEM_TYPE,
    example: ENUM_ITEM_TYPE.PRODUCT,
    required: true,
  })
  @IsEnum(ENUM_ITEM_TYPE, { message: 'Invalid item type' })
  itemType: ENUM_ITEM_TYPE;

  @ApiProperty({
    description: 'IDs of specific items to apply the promotion to',
    example: ['60d21b4667d0d8992e610c85', '60d21b4667d0d8992e610c86'],
    required: false,
    type: [String],
  })
  @IsArray()
  @IsMongoId({ each: true })
  @IsOptional()
  itemIds?: string[];
}

export class ApplyPromotionToItemsDto {
  @ApiProperty({
    description: 'Apply promotion to items',
    example: ['60d21b4667d0d8992e610c85', '60d21b4667d0d8992e610c86'],
    required: false,
    type: [String],
  })
  @IsArray()
  @IsOptional()
  applyPromotions?: string[] = [];

  @ApiProperty({
    description: 'Remove promotion from items',
    example: ['60d21b4667d0d8992e610c85', '60d21b4667d0d8992e610c86'],
    required: false,
    type: [String],
  })
  @IsArray()
  @IsOptional()
  removePromotions?: string[] = [];
}
