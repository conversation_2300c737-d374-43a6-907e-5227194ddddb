import { forwardRef, Module } from '@nestjs/common';
import { AuthModule } from 'src/auth/auth.module';
import { PromotionController } from './controllers/promotion.controller';
import { PromotionService } from './services/promotion.service';
import { PromotionRepositoryModule } from './repository/promotion.repository.module';
import { MongooseModule } from '@nestjs/mongoose';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';
import { Pricing, PricingSchema } from 'src/organization/schemas/pricing.schema';
import { PromotionItemService } from './services/promotion-items.service';

@Module({
  imports: [
    PromotionRepositoryModule,

    MongooseModule.forFeature([
      { name: Pricing.name, schema: PricingSchema },
    ], DATABASE_PRIMARY_CONNECTION_NAME)
  ],
  controllers: [PromotionController],
  providers: [PromotionService, PromotionItemService],
  exports: [PromotionService, PromotionItemService],
})
export class PromotionsModule {}
