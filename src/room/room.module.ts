import { forwardRef, Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { AuthModule } from "src/auth/auth.module";
import { MailModule } from "src/mail/mail.module";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";

import { RoomService } from "./service/room.service";
import { Room, RoomSchema } from "./schema/room.schema";
import { RoomController } from "./controllers/room.controller";
import { Services, ServiceSchema } from "src/organization/schemas/services.schema";
import { Scheduling, SchedulingSchema } from "src/scheduling/schemas/scheduling.schema";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { WaitTimeModule } from "src/wait-time/wait-time.module";
import { Clients, ClientSchema } from "src/users/schemas/clients.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { FacilityAvailability, FacilityAvailabilitySchema } from "src/facility/schemas/facility-availability.schema";


@Module({

    imports: [
        forwardRef(() => WaitTimeModule),
        AuthModule,
        UtilsModule,
        MailModule,
        PassportModule.register({
            defaultStrategy: "jwt",
        }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
        MongooseModule.forFeature([
            { name: Services.name, schema: ServiceSchema },
            { name: Room.name, schema: RoomSchema },
            { name: Scheduling.name, schema: SchedulingSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: Clients.name, schema: ClientSchema },
            { name: FacilityAvailability.name, schema: FacilityAvailabilitySchema }
        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [RoomController],
    providers: [RoomService],
    exports: [RoomService], // Exporting the service for use in other modules if needed
})
export class RoomModule { }