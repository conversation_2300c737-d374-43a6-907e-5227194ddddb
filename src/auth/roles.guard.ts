import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { AuthService } from "./services/auth.service";

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector, private authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const roles = this.reflector.get<string[]>("roles", context.getHandler());
    const request = context.switchToHttp().getRequest();

    if (request?.user) {
      const { user: id } = request.user;
      const user = await this.authService.getById(id);
      request.__user = user;
      request.__organizationId = user.organizationId;
      if(user.role.type === "superAdmin" || user.role.type === "organization") return true;
      return  roles.includes(user.role.type);
    }
    throw new ForbiddenException("policy.error.abilityForbidden");
  }
}
