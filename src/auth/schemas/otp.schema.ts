import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument,SchemaTypes } from "mongoose";

export type OtpDocument = HydratedDocument<Otp>;

@Schema({ timestamps: true })
export class Otp {
  @Prop()
  otp: number;

  @Prop({ required: true, type: String, lowercase: true })
  for: String;

  @Prop()
  createdAt: Date;

  @Prop()
  updatedAt: Date;
  
  @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User" })
  organizationId: string;
}

export const OtpSchema = SchemaFactory.createForClass(Otp);
