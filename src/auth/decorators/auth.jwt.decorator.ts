import { applyDecorators, UseGuards } from '@nestjs/common';
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { AuthJwtAccessPayloadDto } from 'src/auth/dto/jwt/auth.jwt.access-payload.dto';
import { AuthJwtAccessGuard } from 'src/auth/guards/jwt/auth.jwt.access.guard';
import { SessionActiveGuard } from 'src/session/guards/session.active.guard';
import { UserGuard } from 'src/users/guards/user.guard';

/**
 * Extracts the JWT payload from the request.
 * @param data - Optional. Specific field to extract from the payload.
 * @param ctx - Execution context.
 * @returns The entire payload or a specific field if specified.
 */
export const AuthJwtPayload = createParamDecorator(
    <T = AuthJwtAccessPayloadDto>(data: string, ctx: ExecutionContext): T => {
        const { user } = ctx
            .switchToHttp()
            .getRequest<IRequestApp & { user: T }>();
        return data ? user[data] : user;
    }
);

/**
 * Extracts the JWT token from the request headers.
 * @param _ - Unused parameter.
 * @param ctx - Execution context.
 * @returns The JWT token string or undefined if not found.
 */
export const AuthJwtToken = createParamDecorator(
    (_: unknown, ctx: ExecutionContext): string => {
        const { headers } = ctx.switchToHttp().getRequest<IRequestApp>();
        const { authorization } = headers;
        const authorizations: string[] = authorization?.split(' ') ?? [];

        return authorizations.length >= 2 ? authorizations[1] : undefined;
    }
);

/**
 * Extracts the session ID from the request.
 * @param _ - Unused parameter.
 * @param ctx - Execution context.
 * @returns The session ID string.
 */
export const GetSessionId = createParamDecorator(
    (_: unknown, ctx: ExecutionContext): string => {
        const request = ctx.switchToHttp().getRequest<IRequestApp>();
        return request.__sessionId;
    }
);

/**
 * Authentication decorator for JWT access token.
 * Applies AuthJwtAccessGuard and UserGuard.
 * @returns A method decorator.
 */
export function AuthJwtAccessProtected(): MethodDecorator {
    return applyDecorators(UseGuards(AuthJwtAccessGuard, UserGuard));
}

/**
 * Authentication decorator for session protection.
 * Applies SessionActiveGuard.
 * @returns A method decorator.
 */
export function AuthSessionProtected(): MethodDecorator {
    return applyDecorators(
        UseGuards(SessionActiveGuard)
    );
}

/**
 * Full authentication decorator.
 * Applies AuthJwtAccessGuard, SessionActiveGuard, and UserGuard in order.
 * @returns A method decorator.
 */
export function AuthFullProtected(): MethodDecorator {
    // Guards will run in the order they are listed
    // AuthJwtAccessGuard will run first, then SessionActiveGuard, and finally UserGuard
    return applyDecorators(
        UseGuards(AuthJwtAccessGuard, SessionActiveGuard, UserGuard)
    );
}

