import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from 'src/common/pagination/enums/pagination.enum';
import { IPaginationOrder } from 'src/common/pagination/interfaces/pagination.interface';

export class PaginationListDto {
    @ApiHideProperty()
    _search: Record<string, any>;

    @ApiHideProperty()
    _limit: number;

    @ApiHideProperty()
    _offset: number;

    @ApiHideProperty()
    _order: IPaginationOrder;

    @ApiHideProperty()
    _availableOrderBy: string[];

    @ApiHideProperty()
    _availableOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE[];

    @ApiProperty({
        required: false,
        description: 'Search keyword',
        example: 'Search',
    })
    @IsOptional()
    // @ApiHideProperty()
    search?: string;

    @ApiProperty({
        required: false,
        description: "Per page data count",
        example: 1,
    })
    @IsOptional()
    // @ApiHideProperty()
    perPage?: number = 10;

    @ApiProperty({
        required: false,
        description: 'Page number',
        example: 1,
    })
    @IsOptional()
    // @ApiHideProperty()
    page?: number = 1;

    @ApiProperty({
        required: false,
        description: 'Order by',
        example: 'createdAt',
    })
    @IsOptional()
    // @ApiHideProperty()
    orderBy?: string;

    @ApiProperty({
        required: false,
        description: 'Order direction',
        example: Object.values(ENUM_PAGINATION_ORDER_DIRECTION_TYPE),
        enum: ENUM_PAGINATION_ORDER_DIRECTION_TYPE,
    })
    @IsOptional()
    // @ApiHideProperty()
    orderDirection?: ENUM_PAGINATION_ORDER_DIRECTION_TYPE = ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC;
}
