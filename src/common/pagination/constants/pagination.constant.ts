import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from 'src/common/pagination/enums/pagination.enum';

//! Pagination default variable
export const PAGINATION_DEFAULT_PER_PAGE = 20;
export const PAGINATION_DEFAULT_MAX_PER_PAGE = 100;

export const PAGINATION_DEFAULT_PAGE = 1;
export const PAGINATION_DEFAULT_MAX_PAGE = 20;

export const PAGINATION_DEFAULT_ORDER_BY = 'createdAt';
export const PAGINATION_DEFAULT_ORDER_DIRECTION: ENUM_PAGINATION_ORDER_DIRECTION_TYPE =
    ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC;

export const PAGINATION_DEFAULT_AVAILABLE_ORDER_BY: string[] = ['createdAt'];
export const PAGINATION_DEFAULT_AVAILABLE_ORDER_DIRECTION: ENUM_PAGINATION_ORDER_DIRECTION_TYPE[] =
    Object.values(ENUM_PAGINATION_ORDER_DIRECTION_TYPE);
