import { Request } from 'express';
import { Session } from 'express-session';
import { ResponsePagingMetadataPaginationRequestDto } from 'src/common/response/dtos/response.paging.dto';
import { IRequestSession } from './request.session.interface';

export interface IRequestApp extends Request {
    session: IRequestSession;
    __sessionId?: string;
    __isSessionActive?: boolean;
    __delegateSessionId?: string;
    __isDelegateSessionActive?: boolean;
    user?: any;
    __organizationId?: any;
    __user?: any;
    __delegateUser?: any;
    __language: string;
    __version: string;

    __pagination?: ResponsePagingMetadataPaginationRequestDto;
}
