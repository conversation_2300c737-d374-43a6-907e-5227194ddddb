[33mcommit e3678ac6b43c2f3222dee2f130a2161906d78c41[m[33m ([m[1;36mHEAD -> [m[1;32mstaging[m[33m, [m[1;31morigin/staging[m[33m)[m
Author: Navneet <PERSON><PERSON> <maury<PERSON><EMAIL>>
Date:   Mon Jan 27 15:51:49 2025 +0530

    FIX: fix in facilityListFrontDesk filter

[33mcommit 3ff1a61c31bcd1840f730428050d81480161a008[m
Merge: 32bb38f 213c900
Author: abhinavRana-hks <<EMAIL>>
Date:   Mon Jan 27 10:36:46 2025 +0530

    Merge pull request #62 from HKS-Manpower/abhinav-rana
    
    mail for organization when package sold to client

[33mcommit 213c900e0928fe1e1da7b9c719bedf31514977e8[m
Author: abhinav <abhinav<PERSON><EMAIL>>
Date:   Mon Jan 27 10:35:36 2025 +0530

    mail for organization when package sold to client

[33mcommit 32bb38f1d494083b46bdf3ac84046ba00f40dcea[m
Author: Navneet <PERSON>ury<PERSON> <maury<PERSON><EMAIL>>
Date:   Mon Jan 27 09:57:12 2025 +0530

    UPDATE: added type, subtype

[33mcommit de2ec05f4dc058ad3812901f0fffb23aef1392b4[m
Author: Navneet Maurya <<EMAIL>>
Date:   Sat Jan 25 16:36:09 2025 +0530

    UPDATE:: added pagination in get active packges

[33mcommit 290d1d5d3f28052d7374267ca03f0666a0844e8c[m
Author: Navneet Maurya <<EMAIL>>
Date:   Sat Jan 25 16:03:17 2025 +0530

    UPDATE: added createdBy in invoice

[33mcommit d1dccfcad3c570e0df301062df1b4fad3695d8fd[m
Author: Navneet Maurya <<EMAIL>>
Date:   Sat Jan 25 13:54:03 2025 +0530

    UPDATE: changed to optional dob and image in client

[33mcommit 82ef38acd5ed5db9174e38766a22868390cdbee5[m
Author: Navneet Maurya <<EMAIL>>
Date:   Sat Jan 25 13:47:12 2025 +0530

    UPDATE: added billing details in order details

[33mcommit e5e51b92904288ac1d9ee5e90a06ec3ef30b619e[m
Merge: 5d47c41 2585260
Author: Navneet Maurya <<EMAIL>>
Date:   Sat Jan 25 12:25:30 2025 +0530

    Merge branch 'availability', remote-tracking branch 'origin' into staging

[33mcommit 2585260da7733a4303519f9ce62cdf70b9950b37[m
Author: Navneet Maurya <<EMAIL>>
Date:   Sat Jan 25 12:24:47 2025 +0530

    UPDATE: change in availability update

[33mcommit a160e8be4302e33aaa086f12ef448ab6a952f3e2[m
Merge: 793eddb d5b5126
Author: Gaurav <<EMAIL>>
Date:   Fri Jan 24 11:13:02 2025 +0000

    Merge branch 'staging' of github.com:HKS-Manpower/gym-nestjs into availability

[33mcommit 793eddb0f2ecf61fbd7f9b0df726dc6e2a1cd13b[m
Author: Gaurav <<EMAIL>>
Date:   Fri Jan 24 11:09:29 2025 +0000

    FIX: fix of update availibility in multiple

[33mcommit d5b51264c97a1be91619d36488b29f854849b8de[m
Merge: 2b3a2c1 3b192c8
Author: Navneet <<EMAIL>>
Date:   Fri Jan 24 11:53:44 2025 +0530

    Merge pull request #61 from HKS-Manpower/session-scheduling
    
    UPDATE: added validation of booking type in scheduling

[33mcommit 3b192c804b62d6b1d89a05af224bb1eec2928951[m
Author: Gaurav <<EMAIL>>
Date:   Fri Jan 24 06:22:21 2025 +0000

    UPDATE: added validation of booking type in scheduling

[33mcommit 2b3a2c180eeb8aea95ea624b36e4cedec7389a0c[m
Author: Gaurav <<EMAIL>>
Date:   Fri Jan 24 04:43:02 2025 +0000

    FIX: change the working time checking with date time object"

[33mcommit 5d47c41b5f4a5c92a77d702cb6772541619a070c[m
Merge: 0684ce1 92393ce
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 24 09:36:25 2025 +0530

    Merge pull request #60 from HKS-Manpower/staging
    
    Staging

[33mcommit 92393ceb489bf09a4eddd0c70e73229ea23b8d90[m
Merge: 819e826 5e7c0a7
Author: Gaurav <<EMAIL>>
Date:   Thu Jan 23 12:32:41 2025 +0000

    Merge branch 'staging' of github.com:HKS-Manpower/gym-nestjs into availability

[33mcommit 819e82699e1921e7a498b0f35a5928abb0acba73[m
Author: Gaurav <<EMAIL>>
Date:   Thu Jan 23 12:32:28 2025 +0000

    FIX: fix on edit appointment

[33mcommit 5e7c0a78e4a0d3d37504965e20dca24007862263[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 16:42:11 2025 +0530

    fixes

[33mcommit 162c028a8a94a28883e0287003dffe0ce3aba71f[m
Merge: 4ae988b 0c5d656
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 16:37:27 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 4ae988b1c49083f80d3edf934c6804448421d87c[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 16:36:21 2025 +0530

    fixes

[33mcommit 0c5d6569fa8b9c2581177abf42788b1f57ff891f[m
Merge: 648b530 9e14608
Author: Navneet <<EMAIL>>
Date:   Thu Jan 23 16:34:50 2025 +0530

    Merge pull request #59 from HKS-Manpower/availability
    
    Availability

[33mcommit 9e146080eff6bcd3f6ed0220919795980cae4021[m
Merge: 293ce3b e0b696f
Author: Gaurav <<EMAIL>>
Date:   Thu Jan 23 10:58:12 2025 +0000

    Merge remote-tracking branch 'origin/staging' into availability

[33mcommit 648b5308f1f1263fbed7b0e7afc8872eb13ae8ed[m
Merge: bb9078b e0b696f
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 16:26:13 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit bb9078b80ccab25ae07592eeac8bcd8166bb30b6[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 16:26:06 2025 +0530

    fix invoices

[33mcommit e0b696f3b4d36492c5b85059922681af5fbcdf09[m
Merge: cfdb790 4ae9cf3
Author: Navneet <<EMAIL>>
Date:   Thu Jan 23 16:18:13 2025 +0530

    Merge pull request #58 from HKS-Manpower/session-scheduling
    
    FIX: fix of scheduling

[33mcommit 4ae9cf352757d46e59671c559d67966635947f93[m
Merge: 7704576 cfdb790
Author: Gaurav <<EMAIL>>
Date:   Thu Jan 23 10:45:26 2025 +0000

    Merge branch 'staging' of github.com:HKS-Manpower/gym-nestjs into session-scheduling

[33mcommit 293ce3bf8ef575398ddad5b792a724b56391f9ee[m
Author: Gaurav <<EMAIL>>
Date:   Thu Jan 23 10:41:48 2025 +0000

    UPDATE: changed the error message and optmized the method

[33mcommit 445e586b62a689290ca1fca8ea5e99a88cc2979b[m
Author: Gaurav <<EMAIL>>
Date:   Thu Jan 23 10:15:41 2025 +0000

    UPDATE: added validation if you are updating a schedule then it will send a error if a booking is schedule there

[33mcommit cfdb790b3c3f6f5edf55b3063b2e84332b51ff60[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 14:56:54 2025 +0530

    fixes

[33mcommit 0ffc3d392cf64eb6a25197016b0bd0291b4f0ddf[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 14:49:32 2025 +0530

    fixes

[33mcommit 770457676464f80038ea1a2023b64e56e859f2d9[m
Author: Gaurav <<EMAIL>>
Date:   Thu Jan 23 08:18:04 2025 +0000

    FIX: fix of scheduling

[33mcommit b4fb8635ed525f71a7d480a5071d15ce59c1c042[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 13:41:20 2025 +0530

    fixes

[33mcommit 76416a5a8b62cefed41da394da6c8ca4c846b3f7[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 13:37:12 2025 +0530

    fixes

[33mcommit ae73289d469642f616811244da77fc9a151a917f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 13:30:17 2025 +0530

    fixes

[33mcommit 48ffcd196fe33e14ea7ef2a05939b4ae8a51eedb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 13:23:49 2025 +0530

    fixes

[33mcommit 717dada0a65a48f9d9db6c37307dab8ac55f1e7f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 13:21:11 2025 +0530

    fixes

[33mcommit 39dc15dcee2545d9b264f017dcca19075883ee05[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 13:17:32 2025 +0530

    fixes

[33mcommit 594e7d83714790fc2c809714c1fbe1388cbf9261[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 12:20:33 2025 +0530

    fixes

[33mcommit e3e8c77428e6fac2b9dd2b3a1b92d5cf26ee28f7[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 12:13:02 2025 +0530

    fixes

[33mcommit 4a8fff11bec62de8d81453c855c005a311d7e495[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 12:08:44 2025 +0530

    fixes

[33mcommit b5d52fbe9e09b78dacd44dcc8d8dfe465157a664[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 11:57:33 2025 +0530

    fixes

[33mcommit 2335d2dace8db05c7142c227954525f59fec0743[m
Merge: ed52ad2 1b27e8c
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 23 10:54:55 2025 +0530

    Merge pull request #57 from HKS-Manpower/sessionPerDay
    
    Session per day

[33mcommit 1b27e8cbc02740e1ce157bdc1b42f313fad02d0b[m
Merge: ed52ad2 3af266c
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 23 10:51:57 2025 +0530

    add invoice

[33mcommit ed52ad2eac068b440936bacf978a5ce552679526[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Wed Jan 22 15:15:24 2025 +0530

    fix issue

[33mcommit 0684ce1901b51c06bc685242c552dbe506e30beb[m
Merge: c07c10a 0f7fa92
Author: Shivamgupta8476 <<EMAIL>>
Date:   Wed Jan 22 11:59:01 2025 +0530

    Merge pull request #56 from HKS-Manpower/staging
    
    Staging

[33mcommit 0f7fa92482ba711d477105107668147388a80f13[m
Merge: 8928212 4e8812a
Author: Shivamgupta8476 <<EMAIL>>
Date:   Wed Jan 22 11:58:07 2025 +0530

    Merge pull request #55 from HKS-Manpower/sessionPerDay
    
    fixes

[33mcommit 4e8812acc1a82bdca81bc8d2a52f8969e44d94ec[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 22 11:57:17 2025 +0530

    fixes

[33mcommit 8928212e81c430d027af055c14caf2954690c213[m
Merge: d71d336 e95c422
Author: Shivamgupta8476 <<EMAIL>>
Date:   Wed Jan 22 10:43:50 2025 +0530

    Merge pull request #54 from HKS-Manpower/sessionPerDay
    
    Session per day

[33mcommit e95c42246b17ea237114e98b61615c7ce6343207[m
Merge: 9a9badb d71d336
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 22 10:42:06 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into sessionPerDay

[33mcommit c07c10aed7ca38f88e802b5db8088c04e97b0fb4[m
Merge: cad1abd d71d336
Author: Shivamgupta8476 <<EMAIL>>
Date:   Wed Jan 22 10:30:50 2025 +0530

    Merge pull request #53 from HKS-Manpower/staging
    
    Staging

[33mcommit d71d336c6bb771da8a948337a0a0019eae120ed3[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 22 10:30:04 2025 +0530

    fixes

[33mcommit 908a93899123992be009a1641264c216bd4eef09[m
Merge: f604378 6f752f1
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 21 15:34:29 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit f604378f79a233e5f388880e30b52477e0319bca[m
Merge: 562f209 2f21bda
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 21 15:34:11 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 562f209e00c2044cc929190b450424b687e5cd53[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 21 15:33:38 2025 +0530

    fixes

[33mcommit 6f752f19b74953942c7aeb5b9a6e75f895278802[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 21 10:03:09 2025 +0000

    UPDATE: added unlimted with limted sessions and facilityBillingDetails

[33mcommit cad1abd16bee455287b702fbafdfe9845f936620[m
Merge: d4764a9 2f21bda
Author: Navneet <<EMAIL>>
Date:   Tue Jan 21 13:20:43 2025 +0530

    Merge pull request #52 from HKS-Manpower/staging
    
    Staging

[33mcommit 2f21bdae9f7c6c9a3b1642c0aba9049061742d1d[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 21 07:41:10 2025 +0000

    FIX:: fix of response type in schedule details

[33mcommit 11dfb6a48fea946f17a726f630eeb12389fe072c[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 21 07:08:14 2025 +0000

    UPDATE:: skiped the mail sending whilw booking

[33mcommit b7e593297bf43fb066fbd13b8affe74618a80d98[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 21 07:01:31 2025 +0000

    UPDATE: added class type skiping in remove availability

[33mcommit 9a9badbc5a43055f13448d0a09a9034b04769f23[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 21 12:29:30 2025 +0530

    fixes

[33mcommit d4764a9400c7aad79f6cd99a6aa643724b261e19[m
Merge: 00d1093 b64f165
Author: Shivamgupta8476 <<EMAIL>>
Date:   Tue Jan 21 10:41:38 2025 +0530

    Merge pull request #51 from HKS-Manpower/staging
    
    Staging

[33mcommit b64f165fea1c98fae84f5e3ffb06bfb7f4a94dbb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 21 10:39:28 2025 +0530

    fixes

[33mcommit 3af266cdb8fea398f3870d792b4a9e546098309d[m
Author: abhinav <<EMAIL>>
Date:   Tue Jan 21 00:28:25 2025 +0530

    invoice dynamically

[33mcommit d07b55c76c231cd00331c7a39cab0532d2c6b0f4[m
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 17:56:21 2025 +0530

    UPDATE:added customer id in booking listing

[33mcommit a5f4558a39f7809babbf968ba83efc9155f957df[m
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 17:11:59 2025 +0530

    UPDATE:: npm install

[33mcommit 37cc2760c6b7d2ea4f576fe1865779231365f4c7[m
Merge: bc0f3eb f25a301
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 17:07:06 2025 +0530

    Merge remote-tracking branch 'origin/availability' into staging

[33mcommit f25a30153b8923ab32466c4a1c2c5a6f8b91b0a9[m
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 17:03:14 2025 +0530

    UPDATE: added unavailability and schedule check in

[33mcommit bc0f3eb40119b0295e9ab4ee6949a364f3eb9d7c[m
Merge: 67125d5 1f3ce79
Author: shivam-bhardwaj <<EMAIL>>
Date:   Mon Jan 20 16:01:47 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 67125d55a4925a947b38604904009951aafc3316[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Mon Jan 20 16:01:43 2025 +0530

    fix issues

[33mcommit 1f3ce798be4147d6a30fdda243fe379fea1b3e52[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 20 15:34:41 2025 +0530

    fixes

[33mcommit 0ca23aea4fdbc757d2dbfa378e124a9bef725428[m[33m ([m[1;32mstaging-back-20[m[33m)[m
Merge: c5fe7f8 e3dac03
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 20 15:12:50 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit c5fe7f8a828e2236ad99a5221bbdbfc1b29bac2b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 20 15:12:43 2025 +0530

    fixes

[33mcommit e3dac03db54614cf3db45a77ae388e59aba56c6f[m
Merge: 3a8e572 c22caf3
Author: shivam-bhardwaj <<EMAIL>>
Date:   Mon Jan 20 15:05:56 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 3a8e572e50de2f69b8777f82ae25dd440c9677cd[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Mon Jan 20 15:05:17 2025 +0530

    fix issue

[33mcommit c22caf3d6d7a71e3c47d4222ae68615aa5992451[m
Merge: 9c588b4 f0af3f5
Author: abhinavRana-hks <<EMAIL>>
Date:   Mon Jan 20 15:04:52 2025 +0530

    Merge pull request #50 from HKS-Manpower/abhinav-rana
    
    booking and appoitment mail

[33mcommit f0af3f5533fb0b16bf4efa3e0d0850e30c1b0d6b[m
Author: abhinav <<EMAIL>>
Date:   Mon Jan 20 15:04:10 2025 +0530

    booking and appoitment mail

[33mcommit eccc9b92ff007a07100ef6eee2bce8a4df03a331[m
Merge: 6dd3d18 d453454
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 13:34:28 2025 +0530

    Merge branch 'staging' into availability

[33mcommit 8aec0da84d474ff441bdc0043c133622deda9435[m
Merge: 1743611 9c588b4
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 20 13:18:57 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 1743611d1e56e63017e02b4dd7352e2127553c5a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 20 13:18:48 2025 +0530

    fixes

[33mcommit 9c588b4e8dc0dd99cdcf9bf04ef93357e22115fe[m
Merge: da70138 bbdf3e7
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 13:08:38 2025 +0530

    Merge pull request #49 from HKS-Manpower/orders
    
    UPDATE: added cancel and payment status api. Added customer id in ord…

[33mcommit bbdf3e7672d6c43e710830c6fef34e95373f0aa7[m
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 13:04:36 2025 +0530

    UPDATE: added cancel and payment status api. Added customer id in order's list and details

[33mcommit da70138dcc7e62926a332ddd4d43b0ad2611410b[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Mon Jan 20 11:30:08 2025 +0530

    staff disabled issue

[33mcommit 6dd3d189705e32278f44b2d3c6cf152b218fad36[m
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 10:58:13 2025 +0530

    UPDATE: added check of schedule infacility unavailability

[33mcommit d45345485f209e5f3ad5e498d5e197e74880e89c[m
Author: Navneet <<EMAIL>>
Date:   Mon Jan 20 09:06:32 2025 +0530

    FIX: fix of date filter for invoice

[33mcommit f1d57eed49d38a4e44acdd9bd983ef2e33f09a0e[m
Author: Navneet <<EMAIL>>
Date:   Sun Jan 19 10:18:54 2025 +0530

    UPDATE: added fiter for orders

[33mcommit 2b66312643393ed95b63c8390659fc1ad73dd5e1[m
Author: Navneet <<EMAIL>>
Date:   Sun Jan 19 07:42:15 2025 +0530

    UPDATE: added search in invoice listing

[33mcommit 18747f2a0c53314191a1e7d66fe5b284e1897701[m
Author: Navneet <<EMAIL>>
Date:   Sat Jan 18 22:50:53 2025 +0530

    UPDATE: added invoice listing and detail page

[33mcommit e5cc4b13201affdc2371b0f158480778567cef97[m
Author: Navneet <<EMAIL>>
Date:   Sat Jan 18 20:06:33 2025 +0530

    UPDATE: added schedule check in unavailability

[33mcommit 519219ee15364be58db9441891b37cb978c1c519[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 17 15:50:32 2025 +0530

    fixes

[33mcommit 8baa3bf938175104615b9f6f611d8167cfc55ff4[m
Merge: fefec71 81be6b6
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 17 15:32:36 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit fefec718ed7ae38c47afefc57516294f91204920[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 17 15:32:27 2025 +0530

    fixes

[33mcommit 00d1093e45b844b51895d466aad2f49f0bce9b11[m
Merge: 5967be5 81be6b6
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 17 14:50:03 2025 +0530

    Merge pull request #46 from HKS-Manpower/staging
    
    UPDATE: pach in package eligiblity in sessions

[33mcommit 81be6b6a66b85ad62a805e03f41f2dd775a0b14a[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 17 14:49:07 2025 +0530

    UPDATE: pach in package eligiblity in sessions

[33mcommit 5967be572d599df2fc723c76da9a247a65f9f75e[m
Merge: 4e9103c 217b7f0
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 17 14:16:35 2025 +0530

    Merge pull request #45 from HKS-Manpower/staging
    
    Staging

[33mcommit 217b7f06062f50cc78575afde607d998f45fdaa8[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 17 13:53:38 2025 +0530

    UPDATE: change in once a day check in package validation

[33mcommit bf0cabb1ba96df696e412d44af68d283b798942f[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 17 13:42:41 2025 +0530

    UPDATE: added payrate valiation in staff checking in PA

[33mcommit 2d078a2b51d0c054154ba1cb8ecf8e9fc0534628[m
Merge: 712297b 2ccb4c6
Author: abhinavRana-hks <<EMAIL>>
Date:   Fri Jan 17 12:25:11 2025 +0530

    Merge pull request #44 from HKS-Manpower/abhinav-rana
    
    Abhinav rana

[33mcommit 2ccb4c672fd35a6f2ea3692ab993a09f0216b3e4[m
Merge: fe62754 712297b
Author: abhinav <<EMAIL>>
Date:   Fri Jan 17 12:22:30 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into abhinav-rana

[33mcommit fe627549424328f37e2e693ca75356bd9ab91a7a[m
Author: abhinav <<EMAIL>>
Date:   Fri Jan 17 12:22:16 2025 +0530

    sorting in feature tab

[33mcommit 4e9103c8e87430bd99576b107f2cd5a8aa4bf2af[m
Merge: f83433a 712297b
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 17 11:40:29 2025 +0530

    Merge pull request #43 from HKS-Manpower/staging
    
    Staging

[33mcommit 712297b9b4494a1b442f3263e18ae89687761901[m
Merge: 9d9fd3a fe94cee
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 17 11:38:49 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 9d9fd3a30642332197fb3a30f1f70cef59963258[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 17 11:38:41 2025 +0530

    fixes

[33mcommit f83433a80e9791288f8f913282a07a8c9f520523[m
Merge: dc517e0 fe94cee
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 17 11:23:42 2025 +0530

    Merge pull request #42 from HKS-Manpower/staging
    
    Staging

[33mcommit fe94cee451f67a70623ac3d868c144a3056eef3d[m
Merge: bfdf785 60d2e55
Author: Navneet <<EMAIL>>
Date:   Fri Jan 17 11:21:23 2025 +0530

    Merge remote-tracking branch 'origin/staging' into staging

[33mcommit bfdf78518e56a01c5bc28410b4918aadadd9439a[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 17 11:20:49 2025 +0530

    FIX: fix of scheduling  - facility message, cancelation, staff availibilty

[33mcommit dc517e08f65559e5a08bb8fc97735248e1c56971[m
Merge: a957f46 60d2e55
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 17 10:37:19 2025 +0530

    Merge pull request #41 from HKS-Manpower/staging
    
    fixes

[33mcommit 60d2e558609b46667860d440bfbbebf7dcf47e3b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 17 10:35:32 2025 +0530

    fixes

[33mcommit a957f46707a3986bab66ef04715bc73a909744b0[m
Merge: 1be70ec 37487c4
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 17 09:59:28 2025 +0530

    Merge pull request #40 from HKS-Manpower/staging
    
    fixes

[33mcommit 37487c4413ab9b32ee1053e4479312e599e626b3[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 17 09:57:08 2025 +0530

    fixes

[33mcommit 1be70ec56e1c555c6528b6d1d6481761aa1bb93c[m
Merge: 56f2780 2cd6024
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 18:13:50 2025 +0530

    Merge pull request #39 from HKS-Manpower/staging
    
    fixeS

[33mcommit 2cd60247183623354ababc8cbd69ada08860a088[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 18:13:02 2025 +0530

    fixeS

[33mcommit 56f2780a92e8188643fe589203b9b6c7c0715b7f[m
Merge: 4f0b2f1 15d0e3a
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 18:09:45 2025 +0530

    Merge pull request #38 from HKS-Manpower/staging
    
    Staging

[33mcommit 15d0e3ad4db2e7a6bf5d46d0eb53fba5d93bdc11[m
Merge: fa1f6e7 092d141
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 18:08:19 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit fa1f6e71628938e1718285648db748285ddb2867[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 18:08:11 2025 +0530

    fixdes

[33mcommit 092d1419e71ec1c0f9052e8c68f066e215371762[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 18:04:17 2025 +0530

    UPDATE: changed the room optional in session and PA booking

[33mcommit 4f0b2f1a0a6b9e6fab7abd23c4699516e81f985b[m
Merge: 15eb3aa 3abbcfd
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 16:11:22 2025 +0530

    Merge pull request #37 from HKS-Manpower/staging
    
    Staging

[33mcommit 3abbcfd21ddb36e0bfa95290888128ef39d8a1cc[m
Merge: b389ee2 4397942
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 16:10:47 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit b389ee2a6e449f00947e52f81602b132620d7119[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 16:10:39 2025 +0530

    fixes

[33mcommit 4397942ba9d82f757cfde79936bf956a5701ea46[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 16:06:14 2025 +0530

    FIX:

[33mcommit 0606d958ebc4c85cd29593c997ed90a6eeea5f89[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 16:01:53 2025 +0530

    UPDATE: added purchase id in schedule details

[33mcommit 15eb3aab5f87468139836d08bae83652b7e5c3b3[m
Merge: 8bbb52e f369501
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 15:53:14 2025 +0530

    Merge pull request #36 from HKS-Manpower/staging
    
    Staging

[33mcommit f3695018e2ac338240d076b76a3bb0c56b4b1b5c[m
Merge: a521412 dc6fb42
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 15:52:17 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit a5214125a6fa9f903e09937d6517040acc21822b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 15:52:10 2025 +0530

    fixes

[33mcommit dc6fb422fc1eae0a75275a1b1303cb833ccdcc95[m
Merge: 2c67547 61f7c57
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 15:46:50 2025 +0530

    Merge branch 'session-scheduling' into staging

[33mcommit 61f7c57888623f2167dc883bbf304633c0c60528[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 15:45:27 2025 +0530

    FIX: fix of canceled schedules

[33mcommit 8bbb52e7e61adc6b1b25ed5b3d8adcc4d91ebd6a[m
Merge: 3d102f9 2c67547
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 15:39:23 2025 +0530

    Merge pull request #35 from HKS-Manpower/staging
    
    Staging

[33mcommit 2c6754757d1dcb9f3276387417dda737c0ccdd41[m
Merge: 45fcc3a ed5d1b9
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 15:36:37 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 45fcc3aabbf6fe0972c844202e1ceab177c95d66[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 15:36:29 2025 +0530

    fixes

[33mcommit ed5d1b91786d6f791e0f57fb36aa249ce58bbc61[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 13:41:26 2025 +0530

    FIX: fix of trainer detail in schedule details

[33mcommit e2bd82261067a7a47ab769e8517026893b17daa7[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 13:30:12 2025 +0530

    FIX: fix of message of onceaday error

[33mcommit 90db883ba1c462c20ce41619ccad785b4cce3cac[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 13:15:23 2025 +0530

    UPDATE: change of session exhausted message

[33mcommit 3d102f9b3fac99ebb2560c3ae359990db633df59[m
Merge: 85a0446 d5c9d8a
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 12:50:51 2025 +0530

    Merge pull request #34 from HKS-Manpower/staging
    
    Staging

[33mcommit d5c9d8a22c5790b3965ef2beded43cf2da81a19b[m
Merge: 6a756ef d8508b1
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 12:32:28 2025 +0530

    Merge remote-tracking branch 'origin/staging' into staging

[33mcommit 6a756ef9aa2f33a2556aac6d7d9ab52d8eb79837[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 12:31:54 2025 +0530

    FIX: fix again PA

[33mcommit d8508b1d27803d5ff57b168878cdf4db0fb503d5[m
Merge: e2a6b21 a1cad2e
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 12:25:05 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit e2a6b217c0eb21a32eef0e0c4545d0d7ffb922f8[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 12:24:54 2025 +0530

    fixes

[33mcommit a1cad2ef2c9cbe008cb66ff1cd15ff8fe64727ac[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 12:23:09 2025 +0530

    FIX: fix of end date validation in PA

[33mcommit 0c92db4a95d53595ddd7eaf3daa4077be26961ce[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 12:00:26 2025 +0530

    FIX: traner id saving in PA

[33mcommit 85a04469e6dfd036fa8d685c2c61b4029fed4623[m
Merge: 81e98aa 1509802
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 11:55:39 2025 +0530

    Merge pull request #33 from HKS-Manpower/staging
    
    Staging

[33mcommit 1509802d891287d581d852aa4c4d687bebc6166a[m
Merge: c3aab4f e658c4c
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 11:30:47 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit c3aab4f1903c857e31626253e41a13946ba926cd[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 11:29:54 2025 +0530

    fixes

[33mcommit e658c4cb63670c5016a463b5f22c5c92f4acd7e1[m
Merge: 5015244 c569ed6
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 11:20:00 2025 +0530

    Merge remote-tracking branch 'refs/remotes/origin/staging' into staging

[33mcommit 501524410407651303276555009fe96e907592c5[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 11:18:04 2025 +0530

    FIX: fix of cancle of schedule

[33mcommit 81e98aa74aee23d0b292d328f0796d7b3469e55e[m
Merge: 4cda38c c569ed6
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 11:14:34 2025 +0530

    Merge pull request #32 from HKS-Manpower/staging
    
    Staging

[33mcommit c569ed6b9f8826e9b2b4440d6020d549b039e9f1[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 11:14:02 2025 +0530

    fixes

[33mcommit 74461af3325b81037b9a04242d6da05cf94f4ade[m
Merge: c2b38ad 9e51ca7
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 11:04:36 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit c2b38ad1f694040dd622145b3575871f7f476cdb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 11:04:01 2025 +0530

    add new api

[33mcommit 4cda38c5e0c8984bb53da7534afbbb26faa8a0d7[m
Merge: 1c65920 9e51ca7
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 16 11:00:54 2025 +0530

    Merge pull request #31 from HKS-Manpower/staging
    
    Staging

[33mcommit 9e51ca752da67957e99c932d8b2bdeaaae717a8d[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 10:44:44 2025 +0530

    FIX: fix of schedule listing

[33mcommit 5b50e7f41b2e82ee5e380f847a2d4e074b4705b8[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 10:36:54 2025 +0530

    fixes

[33mcommit c65a762d6afcc0abffd208ef44a57468d2cc9635[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 10:24:57 2025 +0530

    fixes

[33mcommit b4e144cb051de6248fd2ea75a3c58d394c6a537f[m
Merge: c7534cd b69a839
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 10:20:50 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit c7534cd88d51c6b4cd390e07468bc1d512ef6875[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 16 10:19:36 2025 +0530

    fixes

[33mcommit b69a839a1fd248a3c30fd07b2f4d701e802d824e[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 16 10:17:35 2025 +0530

    UPDATE: added order listing api and fix purchase date validation in session booking

[33mcommit 1c659200740a5c5dee5ba92d79ac108ad4746116[m
Merge: 3ddb065 9639843
Author: Shivamgupta8476 <<EMAIL>>
Date:   Wed Jan 15 17:58:37 2025 +0530

    Merge pull request #30 from HKS-Manpower/staging
    
    Staging

[33mcommit 96398438a63172904f51a838bc9e94fe4ef3b018[m
Merge: 61441d3 ba0ba51
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 17:56:23 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 61441d38c5ddc40ab604d489d78c3922bd66f748[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 17:56:11 2025 +0530

    fixes

[33mcommit ba0ba5174d3cb9896c0cfff08860262d1314753b[m
Merge: b45c1b5 1fa652f
Author: Navneet <<EMAIL>>
Date:   Wed Jan 15 17:49:00 2025 +0530

    Merge remote-tracking branch 'origin/session-scheduling' into staging

[33mcommit 1fa652f80efb0284d9f119b9f6aacd75d7c8c9da[m
Merge: a630153 021a280
Author: Navneet <<EMAIL>>
Date:   Wed Jan 15 17:38:04 2025 +0530

    Merge remote-tracking branch 'origin/staging' into session-scheduling

[33mcommit b45c1b5127c3c2cf8c2ab02588059dccddc0d94b[m
Merge: 021a280 7b51f47
Author: abhinavRana-hks <<EMAIL>>
Date:   Wed Jan 15 17:37:04 2025 +0530

    Merge pull request #29 from HKS-Manpower/abhinav-rana
    
    room filter issue

[33mcommit 7b51f474e0f9a31ec5e5ca0d8a39a85e605ea91f[m
Author: abhinav <<EMAIL>>
Date:   Wed Jan 15 17:35:56 2025 +0530

    room filter issue

[33mcommit a6301539fc280b8b518fa49b9c14a800b61b55d1[m
Author: Navneet <<EMAIL>>
Date:   Wed Jan 15 17:27:08 2025 +0530

    UPDATE: change in schedule filter and invoice

[33mcommit 021a28001f06e3aba6fe5510ebcc2daf2726860e[m
Merge: 5a7e614 051a687
Author: abhinavRana-hks <<EMAIL>>
Date:   Wed Jan 15 17:08:07 2025 +0530

    Merge pull request #28 from HKS-Manpower/abhinav-rana
    
    room list

[33mcommit 051a68746011f162eaaa0dbc73b1387753c7b513[m
Author: abhinav <<EMAIL>>
Date:   Wed Jan 15 17:06:13 2025 +0530

    room list

[33mcommit 5a7e614c71d22ca9da7f3febd11531670b5eb121[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 16:40:12 2025 +0530

    fixes

[33mcommit 750c8061ac71c3a8b72b97328aedbfd021d5aaa4[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 16:11:37 2025 +0530

    fixeS

[33mcommit e532d3a08098bd6be82755e22e5a99c27afd61e9[m
Merge: 93c2cfb a7a34b6
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 15:23:22 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 93c2cfb2ede8ca7735607f45c619582c8db48fc5[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 15:23:12 2025 +0530

    fixes

[33mcommit a7a34b62c46a202f58de8ea47d15212dc4370538[m
Merge: 48fbbf4 e7def78
Author: Navneet <<EMAIL>>
Date:   Wed Jan 15 15:07:40 2025 +0530

    Merge branch 'session-scheduling' into staging

[33mcommit e7def78d7bd42c2683da0f6fc6b25874e9f7e57b[m
Author: Navneet <<EMAIL>>
Date:   Wed Jan 15 15:06:07 2025 +0530

    FIX: fix of booking and PA for package

[33mcommit 3ddb065c072e10f0e3c036e3aa96e53e73cba2a7[m
Merge: 889cc69 48fbbf4
Author: Shivamgupta8476 <<EMAIL>>
Date:   Wed Jan 15 15:04:05 2025 +0530

    Merge pull request #27 from HKS-Manpower/staging
    
    Staging

[33mcommit 48fbbf45c4c74873c185c61fd5779020439e6f12[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 12:33:38 2025 +0530

    fixes

[33mcommit e5364532ce271ca52fa78bbf3461017b9d89ca3e[m
Merge: 84a7daf 8b68a24
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 12:23:22 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 84a7dafe12a6ba102bdf3c5fbdd61228fc601cfd[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 12:23:14 2025 +0530

    fixes

[33mcommit 8b68a24026e4cfc715657065808df663b926ef38[m
Merge: a92497f 6e21a9d
Author: abhinavRana-hks <<EMAIL>>
Date:   Wed Jan 15 12:17:08 2025 +0530

    Merge pull request #26 from HKS-Manpower/abhinav-rana
    
    room api to fetch the list by facilites id

[33mcommit 6e21a9d8e064e0ff4a5d3e504b433098407942cf[m[33m ([m[1;31morigin/abhinav-rana[m[33m)[m
Author: abhinav <<EMAIL>>
Date:   Wed Jan 15 12:16:19 2025 +0530

    room api to fetch the list by facilites id

[33mcommit a92497f0de15bd2e98cdfd906fa488a1eb558332[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 12:15:00 2025 +0530

    fixes

[33mcommit 83c7644b93c120e7e4aa5c0306d807fae62ce351[m
Merge: a1c0d2f 090bd2b
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 11:19:10 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit a1c0d2f708ae73f637ed5389086984290f6779ea[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 15 11:19:01 2025 +0530

    add new api

[33mcommit 090bd2b9c19b4ab3711aa0af737f1b2fbad14b3e[m
Merge: 7a71f82 2a68c51
Author: Navneet <<EMAIL>>
Date:   Tue Jan 14 17:41:02 2025 +0530

    Merge remote-tracking branch 'origin/session-scheduling' into staging

[33mcommit 2a68c51c048b5279a1a735251d5171b936628df2[m[33m ([m[1;31morigin/session-scheduling[m[33m)[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 14 17:39:54 2025 +0530

    UPDATE: change in PA and listing added name of trainer

[33mcommit 7a71f82e9af649103b06b028ee64099811bf7371[m
Merge: fc666ce b0a1181
Author: abhinavRana-hks <<EMAIL>>
Date:   Tue Jan 14 16:50:04 2025 +0530

    Merge pull request #25 from HKS-Manpower/abhinav-rana
    
    Abhinav rana

[33mcommit b0a11813d491dbcdb2b1a71bf7f7500b6a91e6c4[m
Merge: 077bb81 fc666ce
Author: abhinav <<EMAIL>>
Date:   Tue Jan 14 16:49:39 2025 +0530

    conflict resolved

[33mcommit 889cc69bcaa468774fe5eae252e224790bd08fa7[m[33m ([m[1;31morigin/master[m[33m)[m
Merge: 2ec39ea fc666ce
Author: Shivamgupta8476 <<EMAIL>>
Date:   Tue Jan 14 16:41:17 2025 +0530

    Merge pull request #24 from HKS-Manpower/staging
    
    Staging

[33mcommit fc666cebf13985be2cf183191fbbc8bd12d0e637[m
Merge: 73df61d 1581583
Author: Navneet <<EMAIL>>
Date:   Tue Jan 14 16:09:36 2025 +0530

    Merge remote-tracking branch 'origin/session-scheduling' into staging

[33mcommit 077bb81edeea88953346b3973c334e92a30a0868[m
Author: abhinav <<EMAIL>>
Date:   Tue Jan 14 16:09:20 2025 +0530

    room issue fix

[33mcommit 1581583c04adc5013ebf9ce2e485836b9890763a[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 14 16:08:54 2025 +0530

    UPDATE: fix in pricingListByService and PA

[33mcommit 73df61d0d085b6a1c0bbca2cdc816116da89a358[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 14 15:27:19 2025 +0530

    fixes

[33mcommit 929456963364e394649bef52446f71db22575108[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 14 15:25:34 2025 +0530

    fixes

[33mcommit 8043c8d7be09c8911891600eb0b587d6dad0c4c4[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 14 15:21:29 2025 +0530

    fixes

[33mcommit 96d7c22565dc80182e8ccf67d2986b7e42f7342f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 14 13:43:30 2025 +0530

    fixes

[33mcommit e0b3dc59bfa29a7212302678f2923962ce8ae05b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 14 13:16:29 2025 +0530

    fixes

[33mcommit ba510d7ac1ea005225d42b7b49f042bf2fdff62b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 14 12:45:19 2025 +0530

    fixes

[33mcommit 393df9aca27fb7fb47f6f61b6e40c27c920f7437[m
Merge: f14507e 3f9b359
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 14 12:35:15 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit f14507e8b1981f4779004b9222e1cd056c4ae8e9[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 14 12:35:06 2025 +0530

    add once a day

[33mcommit 3f9b3599f11022d776f2a308ddeef8b973d1d20d[m
Merge: a609751 3a4345b
Author: abhinavRana-hks <<EMAIL>>
Date:   Tue Jan 14 12:31:21 2025 +0530

    Merge pull request #23 from HKS-Manpower/abhinav-rana
    
    Abhinav rana

[33mcommit 3a4345b8ed4e36a3c6831b14b55d5a1f5e2ecd1e[m
Author: abhinav <<EMAIL>>
Date:   Tue Jan 14 12:28:39 2025 +0530

    membership backend

[33mcommit a609751ce71d5fdb604c64a13f96934cbf9b32e1[m
Merge: 5c2bc1d a0f938b
Author: Navneet <<EMAIL>>
Date:   Tue Jan 14 11:56:20 2025 +0530

    Merge branch 'session-scheduling' into staging

[33mcommit a0f938bd34d6bf7184156fd4ccb5603fe0b946d5[m
Merge: b8c5db4 25f40a3
Author: Navneet <<EMAIL>>
Date:   Tue Jan 14 11:55:14 2025 +0530

    Merge branch 'staging' into session-scheduling

[33mcommit b8c5db49cb50204a5d10033ada62e6cfc6014b1c[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 14 11:54:47 2025 +0530

    UPDATE: pre PA commit

[33mcommit 026a4f08459c6b1226c616f06d5197c6f253e8fb[m
Author: abhinav <<EMAIL>>
Date:   Tue Jan 14 11:54:27 2025 +0530

    room delete fix

[33mcommit 5c2bc1d86d7765149c44e8f6976bc0e6ad5e0551[m
Merge: b2c8cd6 1c99315
Author: abhinavRana-hks <<EMAIL>>
Date:   Mon Jan 13 16:42:39 2025 +0530

    Merge pull request #22 from HKS-Manpower/abhinav-rana
    
    Abhinav rana

[33mcommit 1c993155306e0353d6c6a001a69127bc08d8a6fd[m
Author: abhinav <<EMAIL>>
Date:   Mon Jan 13 16:41:26 2025 +0530

    throw error

[33mcommit 9ca2d4b239d7b162603bd05dcca660a9de88492b[m
Author: abhinav <<EMAIL>>
Date:   Mon Jan 13 16:40:31 2025 +0530

    calculate discount value in feature

[33mcommit b2c8cd6e9d5a76571a2790bfa176ec845d4e83b7[m
Merge: 472b15e 25f40a3
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 13 16:09:40 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 472b15e95d06d7c0a7ce709062d886fbdc1b82f4[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 13 16:09:33 2025 +0530

    fixes

[33mcommit 25f40a31716a0a36f4c172a1165355ef496d9be8[m
Merge: b0ff8f0 b9ae895 4e12679
Author: Navneet <<EMAIL>>
Date:   Mon Jan 13 12:30:00 2025 +0530

    Merge branch 'session-scheduling', remote-tracking branch 'origin' into staging

[33mcommit 4e12679b580805e3b778d791c3b44ad7551afe51[m
Author: Navneet <<EMAIL>>
Date:   Mon Jan 13 12:21:57 2025 +0530

    UPDATE: changed the payload type of schedule listing

[33mcommit b0ff8f0fe913790fd296f7b61e090f9ec97890a1[m
Merge: 99bf1b8 1325632
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 13 12:20:32 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 99bf1b80470b067372d881a39cae824a35ed5660[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 13 12:20:21 2025 +0530

    new api

[33mcommit 1325632a23169a2dbd24002b735dc2bcdc8b2308[m
Merge: 238a0db 624473e
Author: Navneet <<EMAIL>>
Date:   Mon Jan 13 10:52:01 2025 +0530

    Merge remote-tracking branch 'origin/session-scheduling' into staging

[33mcommit 624473e310558e9d34d19d62f03d9e4ed962c774[m
Author: Navneet <<EMAIL>>
Date:   Mon Jan 13 10:51:18 2025 +0530

    UPDATE:: added client change in schedule api

[33mcommit 238a0dbcdc6b6a6cec0698c0fa213c2213178a57[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 13 10:05:03 2025 +0530

    fixes

[33mcommit 48272cb489b9d455e15699e13eae6e14fcd50123[m
Merge: 349fd36 ec792bb
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 13 09:52:28 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 349fd36fbae0f6887ad003dfe22e3d1e12959e48[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 13 09:52:20 2025 +0530

    fixes

[33mcommit ec792bb5ea2033081fc994f53ce95323d3dff9d3[m
Merge: be66a2e e803b3a
Author: abhinavRana-hks <<EMAIL>>
Date:   Mon Jan 13 09:11:06 2025 +0530

    Merge pull request #21 from HKS-Manpower/abhinav-rana
    
    feature tab backend implementation

[33mcommit e803b3a46fc1ac7da1abb8c3d43ea040471fc628[m
Author: abhinav <<EMAIL>>
Date:   Mon Jan 13 09:10:07 2025 +0530

    feature tab backend implementation

[33mcommit be66a2e46484e68c372f69efbea038a8d668093b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 13 07:58:39 2025 +0530

    add purchase

[33mcommit c628a23d8bae908077d924dd6eeaabb6c2ffc75b[m
Merge: f92bcbd 665c9d4
Author: shivam  Gupta <<EMAIL>>
Date:   Sun Jan 12 18:36:16 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit f92bcbdfe950753cf98d7abed265cfd5869c489c[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sun Jan 12 18:36:09 2025 +0530

    fixes

[33mcommit 665c9d41c4ecf769e8fae8cae0af5d782a510e4d[m
Author: Navneet <<EMAIL>>
Date:   Sun Jan 12 14:57:49 2025 +0530

    UPDATE: changed the response of create and update api in schedule

[33mcommit 67e33307512f89a9f617dde856694526267ba23e[m
Author: Navneet <<EMAIL>>
Date:   Sun Jan 12 14:32:04 2025 +0530

    UPDATE: changes in listing and detail page

[33mcommit e62260e6a56d607899e827cf792e2b69b413f11f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Jan 11 15:56:17 2025 +0530

    fixes

[33mcommit 6cd580a27f1d7f7699a17db60e9809dc6355b089[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Jan 11 15:14:30 2025 +0530

    fixes

[33mcommit 93f2fe770518f11b29a83c69f58a8074e048f6d8[m
Merge: e8a45a9 84bb47f
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Jan 11 13:42:07 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit e8a45a9a0e041f501d429260ce9c52187e57aab9[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Jan 11 13:41:59 2025 +0530

    fixes

[33mcommit 84bb47fb8e7dceee33c318e8e39a7aedfa5666cb[m
Merge: cde543a a468c88
Author: Navneet <<EMAIL>>
Date:   Sat Jan 11 12:53:17 2025 +0530

    Merge pull request #20 from HKS-Manpower/session-scheduling
    
    UPDATE: added listing and details apis

[33mcommit a468c888340176f4c1b872d60295212ff3109c5f[m
Author: Navneet <<EMAIL>>
Date:   Sat Jan 11 12:51:58 2025 +0530

    UPDATE: added listing and details apis

[33mcommit cde543a5a7dbb2006f84ba9fa919c0a6f6bcd195[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 13:35:32 2025 +0530

    .

[33mcommit a37d26bb9cdd181bb083dac57ab3ab45e93bdbaa[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 13:31:40 2025 +0530

    UPDATE: added remainingSessions in get purchase by user api

[33mcommit 2ec39ea3c4e93605378e0e179ec8f6c77560de92[m
Merge: b9ae895 fa24e38
Author: abhinavRana-hks <<EMAIL>>
Date:   Fri Jan 10 13:20:08 2025 +0530

    Merge pull request #19 from HKS-Manpower/staging
    
    Staging

[33mcommit fa24e38345ab6903988ca1268d17a2863140b37c[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 13:18:23 2025 +0530

    UPDATE: added session count in purchage

[33mcommit 692b29cfe62f8959b9bdc8e63702c33b8c94f88c[m
Merge: 30c402a e675ec5
Author: abhinavRana-hks <<EMAIL>>
Date:   Fri Jan 10 13:09:11 2025 +0530

    Merge pull request #18 from HKS-Manpower/abhinav-rana
    
    room fetch by service category Id

[33mcommit e675ec522c28703a653cf22461e7579c099fffbc[m
Author: abhinav <<EMAIL>>
Date:   Fri Jan 10 13:07:44 2025 +0530

    room fetch by service category Id

[33mcommit 30c402a5bab1fd3f0725e38f998868fada7e0789[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 11:54:28 2025 +0530

    UPDATE: new api added pricing/pricingByUserAndType

[33mcommit 68d78030415ec60c2c5810140d0500f5c8b0d1b6[m
Merge: b212c1f 37bc272
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 11:16:41 2025 +0530

    Merge remote-tracking branch 'origin/session-scheduling' into staging

[33mcommit 37bc272cc24cfef7aa6cf934737f928a133d4b72[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 11:15:59 2025 +0530

    FIX: fix of services

[33mcommit b212c1f6da449a75a2ee66bc9bc9cdb29b9a0f44[m
Merge: 2f8f9f6 683f941
Author: abhinavRana-hks <<EMAIL>>
Date:   Fri Jan 10 11:02:16 2025 +0530

    Merge pull request #17 from HKS-Manpower/abhinav-rana
    
    Abhinav rana

[33mcommit 683f94120f856177ec29a4ffc1b0cf9864aa155a[m
Author: abhinav <<EMAIL>>
Date:   Fri Jan 10 10:59:58 2025 +0530

    fetch room by service

[33mcommit 2f8f9f64adf2109402b3b8d7857caf07e1462622[m
Merge: 22462b5 043c8db
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 10:38:01 2025 +0530

    Merge remote-tracking branch 'origin/session-scheduling' into staging

[33mcommit 043c8db5803a762a37c0853ff402d425a8ef065a[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 10:37:06 2025 +0530

    UPDATE: changed the package listing to post

[33mcommit 22462b54b98d70d589d6436d050fb065532ab9bb[m
Merge: 317ad3e 171594c
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 10 10:25:44 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 317ad3eedb7443b0a452508eca6281c630accc47[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 10 10:25:40 2025 +0530

    fixes

[33mcommit 171594ccbc4a49ef223b8cca8a1502c4248f5a41[m
Merge: e68d036 54b2cde
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 10 10:22:22 2025 +0530

    Merge pull request #13 from HKS-Manpower/session-scheduling
    
    UPDATE: added session sheduling api

[33mcommit 54b2cde69f735378dbfe63dbed8697a3b05eda2a[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 10:19:34 2025 +0530

    UPDATE: fix

[33mcommit 5319596df10262e342f7f019c65436e9797e1ae3[m[33m ([m[1;31morigin/package-changes[m[33m)[m
Author: Navneet <<EMAIL>>
Date:   Fri Jan 10 09:53:14 2025 +0530

    UPDATE: package api changes for package listing

[33mcommit e68d036de72c730d3b0ded084668f5e729880c7e[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 9 17:35:45 2025 +0530

    fixes

[33mcommit e7872d7d657f85ce9f94a0c128c5a6a132a16d06[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 9 16:24:46 2025 +0530

    UPDATE: added validation of remaining session, schedule count and schedule status

[33mcommit b9ae895586043408adea1c429f6b93b6287af57e[m
Merge: 94a851c c1a0276
Author: abhinavRana-hks <<EMAIL>>
Date:   Thu Jan 9 16:16:37 2025 +0530

    Merge pull request #15 from HKS-Manpower/abhinav-rana
    
    add fliter type in room listing

[33mcommit c1a02760a1c466af649e135786285199906ef933[m
Author: abhinav <<EMAIL>>
Date:   Thu Jan 9 16:11:12 2025 +0530

    add fliter type in room listing

[33mcommit 9b4ae272e20aee06d196a772a7febe5cb85e48bd[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 9 14:57:00 2025 +0530

    UPDATE: added update api for scheduling

[33mcommit 8fb5bf0759992b6a8a9722ecd1ac8100b2d22bce[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 9 13:28:36 2025 +0530

    fixes

[33mcommit 94a851cd783ac3b66b25b7aa03585aebb60e1990[m
Merge: 4f2dc16 2427a36
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 9 13:20:06 2025 +0530

    Merge pull request #14 from HKS-Manpower/staging
    
    Staging

[33mcommit 2427a3651d5514aa4f4e7b99b4ac381f685ed516[m
Merge: 9967380 c17394d
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 9 12:19:43 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 99673804bcf4fb919071950c0dbab93520560c18[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 9 12:19:33 2025 +0530

    add pricing api

[33mcommit 9adca419d616242fcc1bbb72a33ca557fb69c5a1[m
Author: Navneet <<EMAIL>>
Date:   Thu Jan 9 11:01:37 2025 +0530

    UPDATE: added session sheduling api

[33mcommit c17394d731f0b4fcca38cba2e6a8e66ea54249f2[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Wed Jan 8 17:59:03 2025 +0530

    staff status update

[33mcommit 243ac7eb39b6274b449c32547ec41ce6443f4dcb[m
Merge: aa09641 1cf7e6d
Author: abhinavRana-hks <<EMAIL>>
Date:   Wed Jan 8 16:44:03 2025 +0530

    Merge pull request #12 from HKS-Manpower/abhinav-rana
    
    change the variable of the room schema

[33mcommit aa09641e1db254918dc6a828e80a6d201954fb8e[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 8 16:42:09 2025 +0530

    fixes

[33mcommit 1cf7e6db5c290c8fa6afcd90244955f94be389f0[m
Author: abhinav <<EMAIL>>
Date:   Wed Jan 8 16:41:48 2025 +0530

    change the variable of the room schema

[33mcommit 29241fec288757eae6259ed6c99b74c2372beadc[m
Merge: 5a6460f 3597e74
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 8 16:28:34 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 5a6460f4a106bd8587cac2551ee8d93270e5428a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jan 8 16:28:29 2025 +0530

    fixes

[33mcommit 3597e748800e5176a04eae1ea5398e523781a2d6[m
Merge: 3ff26c1 2bd4d20
Author: abhinavRana-hks <<EMAIL>>
Date:   Wed Jan 8 13:17:49 2025 +0530

    Merge pull request #11 from HKS-Manpower/abhinav-rana
    
    room backend structure

[33mcommit 2bd4d2058902d8ff410899444271ab3222162146[m
Author: abhinav <<EMAIL>>
Date:   Wed Jan 8 13:15:29 2025 +0530

    room backend structure

[33mcommit 4f2dc162a9f9de9cfd4c9d5fffe7ae768187cf1f[m
Merge: 7f415cf 3ff26c1
Author: Shivamgupta8476 <<EMAIL>>
Date:   Tue Jan 7 13:36:22 2025 +0530

    Merge pull request #10 from HKS-Manpower/staging
    
    Staging

[33mcommit 3ff26c117580f5a0203379ebb07eb81b4e0de529[m
Merge: 3e0b22e cbba9f5
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 7 13:35:24 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 3e0b22e61349438c7cd952f676ad2745e726241a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 7 13:35:16 2025 +0530

    new api for price assign listing

[33mcommit cbba9f543d533647000f3695baa63144c23d22ab[m
Merge: ab9c737 7f415cf
Author: Shivamgupta8476 <<EMAIL>>
Date:   Tue Jan 7 13:25:14 2025 +0530

    Merge pull request #9 from HKS-Manpower/master
    
    pay rate changes

[33mcommit 7f415cf971e02285b3e296659c00830e7566e20a[m
Merge: 3d83b16 279149b
Author: Shivamgupta8476 <<EMAIL>>
Date:   Tue Jan 7 13:24:24 2025 +0530

    Merge pull request #8 from HKS-Manpower/fix-payrate
    
    FIX: fix in payrate create and listing

[33mcommit 279149b6582609f7cfca63dbc1cf2bc4f66a47d2[m[33m ([m[1;31morigin/fix-payrate[m[33m)[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 7 13:20:09 2025 +0530

    FIX: fix in payrate create and listing

[33mcommit 3d83b16eec8fffb81c39457d4c6ae89ff0dcc627[m
Merge: b68fbc1 ab9c737
Author: Shivamgupta8476 <<EMAIL>>
Date:   Tue Jan 7 11:24:19 2025 +0530

    Merge pull request #7 from HKS-Manpower/staging
    
    Staging

[33mcommit ab9c7376d79fcc8f7e7a4cc1c6889ca7fec1f1ac[m
Merge: b70317f 823173f
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 7 11:23:23 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit b70317f799789b106cb8c8953cc7a2065b103ab8[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Jan 7 11:22:25 2025 +0530

    fixes

[33mcommit 823173f271caff5a6f638b4b7d6c5b6e7d283403[m
Author: Navneet <<EMAIL>>
Date:   Tue Jan 7 10:53:17 2025 +0530

    FIX: fixes in payrate listing as issue was at appointment type name projection

[33mcommit b68fbc1dd333dd4b07aabae9c3facb0a9b6c8114[m
Merge: a755a19 c546de9
Author: Shivamgupta8476 <<EMAIL>>
Date:   Mon Jan 6 18:07:45 2025 +0530

    Merge pull request #6 from HKS-Manpower/staging
    
    Staging

[33mcommit c546de97924c5b8f901f2b9c4ba8a381a6f70896[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 6 18:06:55 2025 +0530

    fixes

[33mcommit df0f427ec827654101ac320aff291fe56a682a3b[m[33m ([m[1;31morigin/fff[m[33m)[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Jan 6 14:57:59 2025 +0530

    fixes

[33mcommit a755a19283b937f53b3d3908c8206315582f529f[m
Merge: af3223b b311ff9
Author: Shivamgupta8476 <<EMAIL>>
Date:   Fri Jan 3 17:24:49 2025 +0530

    Merge pull request #5 from HKS-Manpower/staging
    
    Staging

[33mcommit b311ff993021c19d1733e75e16a2a37384f599b1[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 3 15:18:07 2025 +0530

    fixes

[33mcommit 95fb46fd80c9dcfcc744c8bc4a950739d514c525[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 3 13:04:27 2025 +0530

    fixes or otps

[33mcommit 6d553ad6e4990a54368de2a9d5639b2bc1c51d1b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 3 12:25:40 2025 +0530

    add new api for check mobile or mail

[33mcommit a9376ff2ace3dee99c796d6e04a77e42d6f9778a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 3 12:03:08 2025 +0530

    fixes

[33mcommit 1dbdb537a51ef9d0553cc54c601b7173b9466b4c[m
Merge: 4ea2f7f fdab110
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 3 11:55:35 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 4ea2f7f103c32d5cdeaaed3dce442ddda04d15bd[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Jan 3 11:55:30 2025 +0530

    ass sedmail api

[33mcommit fdab110c4180a4d647e1dcb521558b22e78b4a6e[m
Merge: 1325c3e d7ca2a1
Author: shivam-bhardwaj <<EMAIL>>
Date:   Thu Jan 2 16:32:21 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 1325c3ea64c43fb9697b2dab77f83c9a5bbfbd31[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Thu Jan 2 16:32:15 2025 +0530

    attribute fixes

[33mcommit af3223bac27f8b377a807b9a05ff034cbd6a1ff1[m
Merge: ee5b9e3 d7ca2a1
Author: Shivamgupta8476 <<EMAIL>>
Date:   Thu Jan 2 13:27:59 2025 +0530

    Merge pull request #4 from HKS-Manpower/staging
    
    Staging

[33mcommit d7ca2a1abebe614d865070799a594def364fd9a7[m
Merge: 8be670a c8baa72
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 2 13:26:50 2025 +0530

    Merge branch 'staging' of https://github.com/HKS-Manpower/gym-nestjs into staging

[33mcommit 8be670acc30b2f306cf4d7e8cb7a945436812296[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Jan 2 13:26:45 2025 +0530

    fixes

[33mcommit c8baa722f5f42faa74b8c272cc329e25f273da09[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Thu Jan 2 13:09:19 2025 +0530

    attributeId issue fix

[33mcommit ee5b9e3e72f29def6265022fc05b322121c20e06[m
Merge: 7f431c0 d82890a
Author: Shivamgupta8476 <<EMAIL>>
Date:   Tue Dec 31 14:43:20 2024 +0530

    Merge pull request #3 from HKS-Manpower/staging
    
    fixes

[33mcommit d82890afa79a577763785cccc68e48b0e179b787[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 31 14:42:36 2024 +0530

    fixes

[33mcommit 7f431c01b397f6797a37239d090a90b113533f0f[m
Merge: d08f521 cd708e8
Author: Shivamgupta8476 <<EMAIL>>
Date:   Tue Dec 31 12:57:32 2024 +0530

    Merge pull request #2 from HKS-Manpower/staging
    
    Staging

[33mcommit cd708e871d7d86ed62120fd886b8c76d6db48efd[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 31 12:56:18 2024 +0530

    fixes

[33mcommit b1288ba483e49a55a432a685fb7a9d2b3a515d26[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Tue Dec 31 12:01:11 2024 +0530

    issue fix

[33mcommit 5b36d37aae509a6a38e49c467d5ad3efd542c76f[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Tue Dec 31 11:52:25 2024 +0530

    issue fix

[33mcommit 0c512a0ea3c0b969e5a8dd71106f2dc8e0be31c2[m
Author: Navneet <<EMAIL>>
Date:   Tue Dec 31 10:34:30 2024 +0530

    FIX: fix of appointment type linking with appointment types

[33mcommit d08f5215f034b5cb18c268efd2a1dbcb593dbd4e[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Dec 28 19:00:46 2024 +0530

    fixes

[33mcommit 66c770be9cae89a890e421c8161377a8e6aca227[m
Merge: fabeaec dd8c693
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Dec 28 18:40:29 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit fabeaecceec7017490f095627efb214241c760fc[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Dec 28 18:40:19 2024 +0530

    fixes

[33mcommit dd8c693887af68ed252e060eecc97870565f8960[m[33m ([m[1;31morigin/shivam-bhardwaj[m[33m, [m[1;32mmaster[m[33m)[m
Merge: 4bb4e8f af9923a
Author: shivam-bhardwaj <<EMAIL>>
Date:   Fri Dec 27 10:36:00 2024 +0530

    resolve conflict

[33mcommit 4bb4e8fa91ebaa075578b84a7a82d715a9f4e887[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Fri Dec 27 10:30:39 2024 +0530

    appointment type changes

[33mcommit af9923affada0ec44fb4f273488350f69f58decb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 26 15:28:52 2024 +0530

    fixes

[33mcommit ff7ecbb74f556f8fb8046c18275ac36620b15346[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 26 10:42:23 2024 +0530

    fixes

[33mcommit c1099bb41bee4b07a9f63bacfa6cbde726fafa32[m
Merge: 5c2af0a 4e06260
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 24 15:36:09 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 5c2af0aa83ad08f743753fa615b4e08389dad0f0[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 24 15:35:59 2024 +0530

    fixes

[33mcommit 4e06260a1011c58ee739c14b04e1b5b00d7df036[m
Merge: 22ae0dd cf68d1e
Author: shivam-bhardwaj <<EMAIL>>
Date:   Tue Dec 24 15:15:35 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs into shivam-bhardwaj

[33mcommit 22ae0ddfd6242d023da1e665491990a7deb12674[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Tue Dec 24 15:15:23 2024 +0530

    status update

[33mcommit cf68d1e1bb2b83beea10093ac6e05ad194c2cf16[m
Merge: 175cb28 a3f7faf
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 24 15:03:25 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 175cb28c64a9390964c69e86aa2c8eef5a569145[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 24 15:03:19 2024 +0530

    fixed pricing

[33mcommit a3f7fafa1aa699d9226d848c0a7e73ea7411a37a[m
Merge: 75ed0a8 1bdc9ce
Author: shivam-bhardwaj <<EMAIL>>
Date:   Tue Dec 24 11:52:36 2024 +0530

    Merge branch 'shivam-bhardwaj'

[33mcommit 1bdc9ce49d38c3c6a9e14f8505e7e26f14016ff7[m
Author: shivam-bhardwaj <<EMAIL>>
Date:   Tue Dec 24 11:52:06 2024 +0530

    Appointment type fixes

[33mcommit 75ed0a8a9d3e86340812a01bd04927ba778f6890[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Dec 20 10:07:14 2024 +0530

    fixes

[33mcommit 2542745a08e77bbcbcd2199f7062972a52904807[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 19 13:03:51 2024 +0530

    fixes

[33mcommit 5c53abeeec57a037c5b2673b8189b7f51f75b0ea[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 19 12:54:07 2024 +0530

    fixes

[33mcommit 4aa984e7dd848c5dfb9bea993a466c7e1f0150ef[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 19 12:46:28 2024 +0530

    fixes

[33mcommit 11d928059f07edd4d3b697fa9920d3a179304e02[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 19 12:07:19 2024 +0530

    fixes

[33mcommit d697e598a40cc157562e7180ed1ddeb9c9e8d22e[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 19 11:36:06 2024 +0530

    fixes

[33mcommit a3ddef2b435f4443db4a2acfe7642d54bd1fcf79[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Dec 18 16:29:32 2024 +0530

    fixes

[33mcommit ac9ea5d23149b3d85176bde0eb12d621e5f4defe[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Dec 18 15:28:46 2024 +0530

    fixes

[33mcommit 71c4fd890eba5da0465b9744cf25a380f792dd04[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Dec 18 11:10:13 2024 +0530

    add fixes in list api

[33mcommit e7c64e874d753cfce71523ec494bdd082e08c610[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 17 10:34:03 2024 +0530

    fixes

[33mcommit 180dd59bab9357b064cd3c02792be847360d672d[m[33m ([m[1;31morigin/staffAvailability[m[33m)[m
Merge: b4ac582 09206af
Author: Shivamgupta8476 <<EMAIL>>
Date:   Mon Dec 16 17:48:16 2024 +0530

    Merge pull request #1 from HKS-Manpower/staffAvailability
    
    Staff availability

[33mcommit 09206afd24660470e799bea1f4836614ca170c16[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Dec 16 17:46:40 2024 +0530

    add new apis for availability

[33mcommit b4ac582e42e3992822ff67062d3236f03cf868bf[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Dec 13 16:07:51 2024 +0530

    updated code

[33mcommit db075984562020c568150666cccf2f28eb21e571[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Dec 13 16:00:25 2024 +0530

    added

[33mcommit 38b4683d354c23ec71d27e3bb1ad07b9fca4a4e4[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Dec 13 15:56:30 2024 +0530

    done

[33mcommit c8c8e0a174d8cba9a52556af23335f5872bd0fad[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Dec 13 13:37:25 2024 +0530

    updated code

[33mcommit 2e00c7d6a607314820fe6c4aff2208fc482363f7[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Dec 13 13:14:50 2024 +0530

    added access

[33mcommit d41cbabad923236a33ca1732b2561396cb1d8417[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 12 13:06:16 2024 +0530

    fixes

[33mcommit 3cef67bddae0407fb6489894f7f016fd0d4577b6[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Dec 11 12:05:21 2024 +0530

    fixes

[33mcommit 1159857aa3c78109113aa9a807aebd3f3a12a7cf[m
Merge: 108956d 29227d4
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 10 16:08:18 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 108956d598e100c4c525de6762c25168021c600b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 10 16:08:04 2024 +0530

    fixes

[33mcommit 29227d4f42fa4309c4d12ea3ef4f7d267c9a8fc7[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Dec 10 12:59:24 2024 +0530

    commented code

[33mcommit 705c2f5e6b1b9adab9c4f31e9bacd247b3a1318e[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Dec 10 12:57:45 2024 +0530

    made changes

[33mcommit 1b5d1ce59e2dfd2ef15abc4f08198e4ac28dd4fb[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Dec 9 18:00:55 2024 +0530

    issue resolved

[33mcommit 6d56adfaaffa429dc14d60db04510e5d391a8d6c[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Dec 9 17:47:31 2024 +0530

    pricing issues resolved

[33mcommit e9b985955100d78ceffa37ff7a93a09bb30c1523[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Dec 9 16:43:37 2024 +0530

    issue resolved

[33mcommit 6eeab1142c210922badf29e33b18ad56a81d6487[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Dec 9 16:39:00 2024 +0530

    errors resolved and scheduling apis

[33mcommit 4a1d0484046600decf218897b87fccf9874a0829[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Dec 9 15:16:47 2024 +0530

    fixes

[33mcommit 746532601470b881c447103b28e838cc6e40f319[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Dec 9 15:14:06 2024 +0530

    fixes

[33mcommit 15cda2fa34c8f35692996cbb9340e2293e135323[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Dec 9 15:10:29 2024 +0530

    fixes

[33mcommit 89790af6cc156ab7cf9b045b05aff477ba7f3abb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Dec 9 14:51:46 2024 +0530

    fixes

[33mcommit 1f0a25eb67f50ce5b42953c2b1109ad9c5ef8620[m
Merge: ebc9039 9f490e7
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Dec 9 11:51:43 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit ebc9039fb5684967eadc41a3bbd65c6d983d3b91[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Dec 9 11:51:34 2024 +0530

    fixes

[33mcommit 9f490e76d6a644f52260e2ba6ce773a7cc0cc588[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Dec 6 12:04:27 2024 +0530

    made changes

[33mcommit 8f90e0af765127580fb66852a93481eaa05b8196[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Dec 6 12:00:18 2024 +0530

    made changes

[33mcommit 246f68dd6b349badc91954192a560b8e584f0f3c[m
Merge: f100c4c bedc34f
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 5 15:12:02 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit f100c4c2d1f216c55b77fd9507ff733fc7bd65fb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 5 15:11:51 2024 +0530

    add classType in availability

[33mcommit bedc34f5354f195085aeea6827a548fb75ecf3d0[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Dec 5 12:51:27 2024 +0530

    added skelton

[33mcommit 24a19cf1cc5f1fd110aa9a6e9d91ffcd6d5750d6[m
Merge: 3be3aa6 de8999b
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 5 11:57:37 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 3be3aa6240a3f1fd495992178f6c5b2a099336fc[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Dec 5 11:57:28 2024 +0530

    fixes

[33mcommit de8999bcc72670c68dda451cccb7b63c11f3c2a9[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Dec 4 13:17:16 2024 +0530

    pricing api

[33mcommit d6858a9cac293a768cdf1e39e7ea09cd8543579b[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Dec 4 11:47:46 2024 +0530

    added headers

[33mcommit e03053abd3a893cc5d0c1587b4166529fdb326ba[m
Merge: f588205 a3aa4d9
Author: Shivam Monga <<EMAIL>>
Date:   Wed Dec 4 11:44:05 2024 +0530

    Merge branch 'stashedBranch' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit f5882050f3e0126dc47510f9ff7e14b3c8724725[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Dec 4 11:43:01 2024 +0530

    classes scheduling skelton

[33mcommit a3aa4d9da3c429ec1d42aee5053e859265dc1903[m[33m ([m[1;31morigin/stashedBranch[m[33m)[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Dec 3 15:34:23 2024 +0530

    resolved issues

[33mcommit 2d35f1e06e5ea42f9bc3ea1c37fb5d7cd7c56957[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 3 10:47:56 2024 +0530

    modify update pay rate api

[33mcommit 0d703fc75e202c7dd34ce32b44c189da6398f1df[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Dec 3 10:34:06 2024 +0530

    removed classes

[33mcommit 8254f170682e8634e316bca65f10ec6e7eed3469[m
Merge: 9728025 bc9ca30
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 3 10:33:41 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 9728025edbfd198b12fd979dd9659582b05ca5c0[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Dec 3 10:33:32 2024 +0530

    fix parrates api

[33mcommit bc9ca30a910830f301d8c8344ce5e5ec9a132e60[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Dec 3 10:21:15 2024 +0530

    error message changed

[33mcommit 4f05a5fbea4b2fbbf5f8a64a749feca194d6238c[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Dec 3 09:19:19 2024 +0530

    fixes

[33mcommit 2c2621db8eb5ad9cb34d0ebc7598e41c087a6ad0[m
Author: abhay-rana <<EMAIL>>
Date:   Mon Dec 2 16:22:27 2024 +0530

    fixes

[33mcommit f2c2744dbdecadae6fbd3970ab573f816c8bcfa4[m
Author: abhay-rana <<EMAIL>>
Date:   Fri Nov 29 10:57:41 2024 +0530

    fixes

[33mcommit 6395aa2d37dc46394b0ae5f062c93e751c4dc72d[m
Author: abhay-rana <<EMAIL>>
Date:   Thu Nov 28 12:01:20 2024 +0530

    fixes

[33mcommit d7106b81b7759dc0ba2967516b62442c49ecb875[m
Author: abhay-rana <<EMAIL>>
Date:   Thu Nov 28 11:29:11 2024 +0530

    fixes

[33mcommit 69681ed057f7897150236dc54b7b7302a64df101[m
Merge: 6b6a606 b430bd8
Author: abhay-rana <<EMAIL>>
Date:   Thu Nov 28 11:08:46 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 6b6a60678ff5dffb9ef21a4a54d008134ab055db[m
Author: abhay-rana <<EMAIL>>
Date:   Thu Nov 28 11:08:33 2024 +0530

    fixes

[33mcommit b430bd872084b900bc15a487b515c58c7be2d77d[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Nov 28 11:01:25 2024 +0530

    resolved issues

[33mcommit 990206aae8e480400b605cf70746efab781d198f[m
Merge: a797c76 f8ccede
Author: abhay-rana <<EMAIL>>
Date:   Thu Nov 28 10:49:37 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit a797c760e854cc6e37aa2390b23ca34871130554[m
Author: abhay-rana <<EMAIL>>
Date:   Thu Nov 28 10:49:30 2024 +0530

    fixes

[33mcommit f8ccede014ee4ca61defa3c1c9f19e1dce342376[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Nov 27 16:22:57 2024 +0530

    issue resolved

[33mcommit 8a04ab45d25876eff75a5a6bde7e936d1b742484[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Nov 27 16:21:39 2024 +0530

    client issue resolved

[33mcommit c8c204dc9056c01ea3d691b622b6bc72a9d3cc30[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Nov 27 16:16:13 2024 +0530

    staff reset done

[33mcommit 731fc121cfe54c153b8209ad0b56190567544fca[m
Merge: b652e0e 2843366
Author: abhay-rana <<EMAIL>>
Date:   Wed Nov 27 15:26:20 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit b652e0e9dbdbfec6b2d4ac96833f9003f517a5a1[m
Author: abhay-rana <<EMAIL>>
Date:   Wed Nov 27 15:25:32 2024 +0530

    fixes

[33mcommit 28433666eceff04b55eb17992b49d26e640e5ef8[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Nov 27 13:44:50 2024 +0530

    issue resolved

[33mcommit fbccab706092e809b542e500d2364b4af5ff27af[m
Author: abhay-rana <<EMAIL>>
Date:   Wed Nov 27 10:27:36 2024 +0530

    fixes

[33mcommit 1e3b977f42adecb4e0de9cc6b7b30cbd102140a4[m
Merge: 5a4bb82 e4d74ab
Author: abhay-rana <<EMAIL>>
Date:   Wed Nov 27 09:04:38 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 5a4bb82dbcf9da74bcced9f3324d26287432c410[m
Author: abhay-rana <<EMAIL>>
Date:   Wed Nov 27 09:04:30 2024 +0530

    fixes

[33mcommit e4d74aba27c3cc0f324e706089de532b113377fd[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Nov 26 17:53:19 2024 +0530

    resolved set password

[33mcommit d615d3608435814f9f760a7dcf248e581d660c52[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Nov 26 17:30:17 2024 +0530

    made changes

[33mcommit 7b84b2ec19b127bf444a90a537be047b428fafb7[m
Merge: ceb36d1 986aba2
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 16:37:10 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit ceb36d1ce34831a9d0038701480bb4a6971d803a[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 16:37:02 2024 +0530

    fixes

[33mcommit 986aba2a0b7f2420d59d3a2c87252b1587f84b7f[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Nov 26 16:31:46 2024 +0530

    issues resolved

[33mcommit 04832508c28cdea6fa927221f3cb24cd9b03588a[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 16:22:44 2024 +0530

    fixes

[33mcommit 62996ca004ee4df805aed6d6a299552235171943[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 15:43:16 2024 +0530

    fixes

[33mcommit 2a5c66db40c32926425942c41533fab035716373[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 15:38:49 2024 +0530

    fixes

[33mcommit 5517a5ce14b150dd2b6d26164f2ff801edba64cf[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 15:33:43 2024 +0530

    fixes

[33mcommit 50f149bb58f9940390dec8aa304b40ed6d3962dc[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 15:02:45 2024 +0530

    fixes

[33mcommit 99670fcc7ef7af93d06d9325eaf4c165fa4bc7c3[m
Merge: 369fc6f 741c19d
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 14:53:11 2024 +0530

    Merge branches 'master' and 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 369fc6fbf3516def0b41369b35fd4c83de264ad2[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 14:52:41 2024 +0530

    fixeS

[33mcommit 741c19d8a20aceceda076f66cb08b14be57ccc4a[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Nov 26 13:37:00 2024 +0530

    resolved issue

[33mcommit 8ea5ffb38f53f5fb27c00daf1eca9d676a3978d5[m
Merge: 990b369 8f3e68c
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 12:50:46 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 990b3694fc2a5932356f3bfdeb71cbcb5cc5335e[m
Author: abhay-rana <<EMAIL>>
Date:   Tue Nov 26 12:49:00 2024 +0530

    add prices

[33mcommit 8f3e68cacc5571355902817f18ca1c8fcd4d681b[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Nov 26 12:48:34 2024 +0530

    updated names

[33mcommit ddf3028ec891c890df36236a381d34802213771c[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Nov 26 11:23:56 2024 +0530

    resolved issues

[33mcommit a9400219b3ef70107abded0a792add9e6ccbac4a[m
Merge: 56f2858 a410d69
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Nov 26 09:34:35 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 56f28588ae4436ac5116c6e85e5d985c0e463d0c[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Nov 26 09:34:26 2024 +0530

    add purchasing

[33mcommit a410d69c4338ede972e2d388ff8007485a257229[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 25 15:33:51 2024 +0530

    by default status would be false

[33mcommit 98ebe904596f0583bc4130de3ee3b440ee4ba8bb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Nov 25 13:16:36 2024 +0530

    fixes

[33mcommit 4b8aee098d45a36e18b7b5e64c0852fa323091cc[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 25 11:56:30 2024 +0530

    resolved issue

[33mcommit 56271bdd60da9eea014bcfabd6e9c5ec87ed0e0f[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 25 11:47:40 2024 +0530

    made changes:

[33mcommit b8a2cffd266513a71b215790b466d96a225c5ed0[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Nov 22 13:03:32 2024 +0530

    bulk unavailability added

[33mcommit dbe2e9bbc6f05c3cb29d7206faf7616ae83bb361[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Nov 21 17:44:39 2024 +0530

    updated pricing

[33mcommit 8c8b4a031b255d6244a931bdc5b5088c27c35b8a[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Nov 21 12:37:28 2024 +0530

    incomplete book pa

[33mcommit ae6637f3cfee0945370c9ccc8e7c8547d0b4d153[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Nov 19 10:02:10 2024 +0530

    fixes

[33mcommit 943f851a00e5e29c4e8463822efa08d03ced59d1[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Nov 19 09:59:37 2024 +0530

    fixes

[33mcommit 778689c59dce3d14796ec2c4cc9f604e5c60c63a[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 18 17:46:30 2024 +0530

    resolved issue

[33mcommit fca669beea85f3a873f17c3d2cc8770f4f36fdbc[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 18 17:42:59 2024 +0530

    resolved issues

[33mcommit 9ebe2d9a5f68525897eb36d3ff6e35ca54ad80f6[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 18 17:42:00 2024 +0530

    made changes

[33mcommit 5f6735acf12454aa84ce16c382c28095b4088599[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 18 17:36:39 2024 +0530

    updated appointment details api

[33mcommit b121d0bc0bc1cfb89311884073205f2fbea07fdc[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 18 17:28:39 2024 +0530

    updated enum

[33mcommit 05ce57f3a9bd65c6c80db80cd6dc7c8e4d024d85[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 18 15:12:25 2024 +0530

    resolved

[33mcommit 45c7b8ca88a1d6e6f61740eb6585edeb6b901610[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 18 13:19:31 2024 +0530

    api updated

[33mcommit 60a30b7917b4a46d36d80cc1ddaaac83fc97f77b[m
Merge: ec4dcaa 63b8dc1
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Nov 18 12:11:19 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit ec4dcaaea2fe47c8481aa4f1f313b5045c8effe8[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Nov 18 12:11:11 2024 +0530

    fixes

[33mcommit 63b8dc1af2791a4acd82b7c1d29972cfab7e609d[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Nov 15 17:39:56 2024 +0530

    module setup

[33mcommit e54c4dd346459a7daa7b6c2b40d23afdf82771f8[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Nov 15 15:24:13 2024 +0530

    updated schema names for pricing

[33mcommit 7ff49c2fe04415768c55d129b6179512d884dd7b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Nov 15 14:59:26 2024 +0530

    fixes

[33mcommit 07faffc0e978c4ce42f260b4d8a2f5c8611a8af7[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Nov 15 13:42:03 2024 +0530

    fixes

[33mcommit 111617c34ce355cd664c6baa328d77d57c07c117[m
Merge: 3b1e1a2 39b898b
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Nov 15 12:43:14 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 3b1e1a25058c02d7d897b72842ef46769c983252[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Nov 15 12:42:20 2024 +0530

    fixes

[33mcommit 39b898bd02d21f239482d4f6e70b7ad9647962ac[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Nov 15 12:03:29 2024 +0530

    resolved expiration

[33mcommit 1ffaf17ce219c5174c66d8b9d11eafc1b9da035f[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Nov 15 11:52:30 2024 +0530

    pricing linkage is working

[33mcommit d72e605977b9b22b275b9c4a45f47967b0498075[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Nov 14 18:00:29 2024 +0530

    pricing linkage

[33mcommit 27c55760478e0405b6ad5edc3cebe1aa92e8aa90[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Nov 13 17:26:43 2024 +0530

    resolved issue

[33mcommit 47ed8d672169f64a3d407946d6e10cf197cf0dd5[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 11 15:30:06 2024 +0530

    facility unavailability api's updated

[33mcommit 5786abfbb438ec0b1256a3beb7542e79fa328ea0[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 11 13:35:19 2024 +0530

    updated add unavailability api

[33mcommit 736e17226900d4ad33b28a3eca6d08b6cb32c9f9[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Nov 8 17:55:39 2024 +0530

    fixes

[33mcommit bc5870424168d61ebab4bc0ae7937e1ecb76f1b6[m
Merge: cc37ce5 fe250dc
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Nov 8 15:51:40 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit cc37ce5dc58974882251fc1e56a5e573d0ad48eb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Nov 8 15:51:31 2024 +0530

    fixes

[33mcommit fe250dcb742c960a292668ce032a6bb4aa4ff95d[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Nov 5 14:55:11 2024 +0530

    updated schema

[33mcommit f8709e97647188996fc2a2ac7d70667039b564a3[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Nov 5 13:05:59 2024 +0530

    updated code

[33mcommit 10e4cc4085cbb051390bec92e395a200e4396bd7[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 4 15:03:31 2024 +0530

    updated access to webmaster

[33mcommit 926173427f0a83ef7501290eae027fdd5cebfb62[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Nov 4 00:20:56 2024 +0530

    classes crud is completed

[33mcommit e02490c8a12b395b2dcafb3d72702435edeed26a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Oct 30 13:31:47 2024 +0530

    fixes

[33mcommit 558cc54b24713f95d6e32b5a78c1f30000fb9ae8[m
Merge: 5c51770 8b8704f
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 15:59:27 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 5c5177002d66cf42b521354815c9cdc7cfee1644[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 15:59:16 2024 +0530

    fixes

[33mcommit 8b8704fe1689f24a85b5411313fd1dee303efa82[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Oct 29 15:16:02 2024 +0530

    updated code

[33mcommit c323471be6ce1262dbbc339fdb63398b580ae711[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 13:06:03 2024 +0530

    fixes

[33mcommit 9804330e22cc93710d6916a36d7ae1fcf06d0ccb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 12:21:46 2024 +0530

    fixes

[33mcommit 47d89afffd1619f8d36027e31fc2a5329daa5c98[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 12:02:44 2024 +0530

    fixes

[33mcommit d1d636a0a5c356fd7d28d53bb57db4f8503ca182[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 11:18:29 2024 +0530

    fixes

[33mcommit 788dc70447c97988bd6282c2236d176a6d71a726[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 11:01:51 2024 +0530

    fixes

[33mcommit 6c52f8addfe539cdcc733f76b7d0aad7ea57b17b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 10:53:05 2024 +0530

    fixes

[33mcommit 4ae2fd14125304bfa8165c7b3d22f03c52a54bc4[m
Merge: 4d9d5cc 7cbc48e
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 10:21:53 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 4d9d5ccfaa2d3c247dd9fff110fe23ccc44ae971[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 29 10:21:41 2024 +0530

    fixes

[33mcommit 7cbc48e63cf56cb57946ef38425dc1274ccc7eb0[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Oct 26 16:54:27 2024 +0530

    get details working

[33mcommit d1a94aed192c37e31003fa3bde212333a9c32912[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Oct 26 16:01:10 2024 +0530

    fixers

[33mcommit 3be2ae85eef8b8f56d131ba5a413fa6867ef4e7f[m
Merge: dd3eed2 414de42
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Oct 26 15:15:49 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit dd3eed23c6245a6d1f31ca89d42bb46ed8222a93[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Oct 26 15:15:40 2024 +0530

    fixes

[33mcommit 414de4229c7f63250d2ee598262cc05c0ab83e33[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Oct 26 13:44:50 2024 +0530

    made image optional for org

[33mcommit 02b0112be8fa85cf9c0d5e44cd74a7b40ac5eeb9[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Oct 26 11:10:48 2024 +0530

    fixes

[33mcommit 39d8b3b26d1e349253f12b2d97d348ac6ec4e7fd[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Oct 25 16:41:31 2024 +0530

    fixes

[33mcommit 3ae1ff0e121b97becfbaa97361bb3753a54d6d9e[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Oct 25 16:29:18 2024 +0530

    fixes

[33mcommit 61a8d5058ac082573b35c11b3498b42b1d2653d9[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Oct 25 16:20:57 2024 +0530

    fixes

[33mcommit 4298207ff445016d9e2fb7ea60802c533030c2e3[m
Merge: 61c5a1f 15cd23c
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Oct 25 15:58:09 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 61c5a1f34c247df18ee67535faa15f733eeaf7e8[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Oct 25 15:58:00 2024 +0530

    fixes

[33mcommit 15cd23c554d10bb63334fe7a6060d826fca0f0c8[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Oct 25 14:53:05 2024 +0530

    updated code

[33mcommit b9556c09812b7df05e2e781fe37ba61002239812[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Oct 25 14:49:26 2024 +0530

    details updated

[33mcommit 5d0da85c25d9d923c3cd5cf6abe10aab9bed0bbb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Oct 25 11:56:18 2024 +0530

    fixes

[33mcommit 60f8c46dedac92a320edcc530323b638ccbbdad0[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Oct 24 15:40:56 2024 +0530

    fixes

[33mcommit 4ace5e4b9a9a2f9e0d9a2afd3c80387a9540b573[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Oct 24 11:57:48 2024 +0530

    fixes

[33mcommit b26e8cc4c11588518ee1c663c728282272ecea74[m
Merge: 0c7d9cd 20a0876
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Oct 24 10:37:47 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 0c7d9cdfe36102f4b424042bd8935e558c449807[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Oct 24 10:37:37 2024 +0530

    fixes

[33mcommit 20a0876a15bed7d22483cdff545d987933a91181[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Oct 23 13:29:54 2024 +0530

    updated code

[33mcommit f2ff34216cd9f47b061f505d5ed557cb46769b96[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Oct 23 11:30:56 2024 +0530

    fixes

[33mcommit ac00bcc96aa7cd602ab39b622bc076cce8231a1a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 22 17:47:39 2024 +0530

    fixes

[33mcommit ddd852df1c710214882597ea1dfb36484e91c747[m
Merge: 01597b2 d7ed3e5
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 22 15:49:18 2024 +0530

    fixes

[33mcommit 01597b2fd37d4ad470465abdd912790aa80c8d13[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 22 15:40:43 2024 +0530

    fixes

[33mcommit d7ed3e5f43a63d465de97141730c0ba18f82ff68[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Oct 22 13:37:14 2024 +0530

    improved message

[33mcommit e35b7e10f031bd9031c5cbee89904d9b866b9e28[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Oct 22 12:10:17 2024 +0530

    updated documentation

[33mcommit 8f0d38639a99b1295886e8c8bfb86b7e9330e6ae[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Oct 18 16:24:54 2024 +0530

    Pricing crud is complete

[33mcommit 15850082b55f309d4bae48827d83fca65b15b222[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Oct 18 10:42:12 2024 +0530

    updated comment

[33mcommit f5d04d6a848be6f8199a5965ce89c6dab8639ff3[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Oct 18 10:40:41 2024 +0530

    added apis

[33mcommit fb3642455f6a3bbfa748ef409ee84c2200a40036[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Oct 10 16:30:59 2024 +0530

    resolved error

[33mcommit b111950066b0e691df742cf4f7d7f64df2b793c8[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Oct 10 12:24:48 2024 +0530

    resolved issue

[33mcommit f7e63fd988f36c5ec69bb0ba98d9dc6324984320[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Oct 9 13:23:59 2024 +0530

    resolved commit

[33mcommit 5a6da515381bdf09acef3c4d0e0c7203653ce736[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Oct 9 13:23:03 2024 +0530

    service category crud is done

[33mcommit 6077250852404a9c2d692097aad56a947d82f4eb[m
Merge: 982060f 9376e6b
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 8 10:52:35 2024 +0530

    merge conflicts

[33mcommit 982060fd049760334ea7002d7dab3f7ef64928ad[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 8 10:50:44 2024 +0530

    add getstaffdetails-api

[33mcommit 9376e6b12bbdffd5a12a5355e598f9387eb6853f[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Oct 7 15:56:37 2024 +0530

    updated project

[33mcommit 26e7337164ff90b95321948e81c216339b96217b[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Oct 7 13:47:47 2024 +0530

    added api for client list for trainers

[33mcommit 4e902a3971f36b636f3d993433225eeb90964431[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Oct 4 10:31:06 2024 +0530

    updated code

[33mcommit f7fc17fa920f7affb844b3178ce9cb8ad555ae1a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Oct 4 09:56:20 2024 +0530

    updated code

[33mcommit 217d9ce71dc55b2f9607fd6d4a2f63ec07bfa858[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Oct 4 09:52:14 2024 +0530

    updated code

[33mcommit b35d95d65fd342d2edee6d1aece7df12fe2c0545[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Oct 3 15:05:50 2024 +0530

    added api for details

[33mcommit 706090c53b089da9c1e94d336653b196c2b419bd[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Oct 1 12:03:35 2024 +0530

    resolved issue

[33mcommit e7dd1e7b10f652f676592c5f21adc7bb3ee24693[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Oct 1 11:53:45 2024 +0530

    added api

[33mcommit 804e5ecb18197fe9099c6e70488ccdc7072f2720[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Oct 1 10:28:36 2024 +0530

    fixes

[33mcommit 7699c438c48b7cc128f0af2ca56a9dd6bfb555c2[m
Merge: 41e3976 c62458e
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 30 17:36:48 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 41e3976f7c82230b77fc3ad473525c01a5ddbbd3[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 30 17:36:40 2024 +0530

    fixes

[33mcommit c62458e148f6b90b7da05428fca6a7bf8e255191[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 30 16:44:15 2024 +0530

    completed api

[33mcommit bc2936f8e9ea8a794f979bb5cef712538f5f887d[m
Merge: 6766630 b990765
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 30 16:43:19 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 6766630a339e9b75de7c11f4b1010167f05afaee[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 30 16:43:11 2024 +0530

    fixes

[33mcommit b990765b1dc73842bff909e916e093b57d06892a[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 30 16:08:04 2024 +0530

    updated swagger

[33mcommit 8db49db3baffcd3e94fd2536465a995ecb380571[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 30 16:04:06 2024 +0530

    facility availability updated

[33mcommit c535d0a4e0702ec8a7f7707c80b9724e8e3eba57[m
Merge: 9f81942 7288502
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 30 15:09:04 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 9f81942e736f4116438cb770e94df3ffc3933449[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 30 15:08:57 2024 +0530

    fixes

[33mcommit 728850277f129970872cab828ce1dc25c823f8a7[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 30 13:22:44 2024 +0530

    facility availability created

[33mcommit f446d3418462be00e0f196a7eebc39fc9f1e6116[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 30 12:00:45 2024 +0530

    fixes

[33mcommit 96d12515094eb3885167d8cee8770b4fd3537ef0[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 30 10:40:20 2024 +0530

    fixes

[33mcommit 0d2677848b9bd48eebc20908a5697b73d52f5762[m
Author: shivam  Gupta <<EMAIL>>
Date:   Sat Sep 28 15:00:06 2024 +0530

    create api of availability

[33mcommit 59b3eeca25ce440d63b3e0b293ff3a2d5b8e05ef[m
Merge: caf3baf 8ebe3e9
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Sep 27 11:26:15 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit caf3bafbe6f57873ed1a0b2ab8a3a83b750f7b4f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Sep 27 11:26:04 2024 +0530

    fixes

[33mcommit 8ebe3e96e4548c1958edff43751a4566b5575b61[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 25 10:29:37 2024 +0530

    updated project

[33mcommit 7ee549feae76718e22d49be2cafd62e109c1e3df[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 25 09:51:47 2024 +0530

    fices

[33mcommit 9044972cb17392d5b1349db10c5598e052e89b44[m
Merge: 515a67e 1b322f3
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 25 09:44:22 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 515a67e12d4b0ed40f2087bbdabc2b95270ee37e[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 25 09:44:14 2024 +0530

    add delete api

[33mcommit 1b322f3813308eff8b799044e79302509a228180[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 25 09:37:23 2024 +0530

    facility availability done

[33mcommit 9e93cc215097eaa2a3c614681ea04d6cc163a16a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Sep 24 17:55:14 2024 +0530

    fixes

[33mcommit 6774867a4732e4da3d3c0a368998c791f3dc50ae[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Sep 24 17:30:25 2024 +0530

    fixes

[33mcommit 5c18894801f265c71cb261627036e2cff32fb4df[m
Merge: 4b5ea3f 8bdfc15
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Sep 24 16:15:50 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 4b5ea3f6f70aa3cfd0297af4db4b81d6e7c2c65f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Sep 24 16:15:39 2024 +0530

    add staff availability api

[33mcommit 8bdfc159d13c41dbf4b501af8fda367d2243994d[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 24 10:22:32 2024 +0530

    updated code

[33mcommit e55aaab22930833d3e6f4887d3cc09781730abd0[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Sep 20 15:37:51 2024 +0530

    condition updated

[33mcommit e84c9c295ef6b9b6ca256776008ea1d9f3953a2f[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Sep 20 13:26:40 2024 +0530

    resolved issue

[33mcommit 58677f1f7c1a8da6d0fc45441113ed63f85ebf29[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Sep 20 13:21:53 2024 +0530

    resolved issue

[33mcommit a3b46c2c72833b01ca819fed74f8b42278dfc145[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Sep 20 12:53:23 2024 +0530

    resolved user onboarding issue

[33mcommit 5df69e09b4147147563ca8bb8c2c84c257de48e0[m
Merge: 696a470 7672456
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 18 17:49:47 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 696a47098ef91cf10a2f6593cf638ec800f24b1a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 18 17:49:39 2024 +0530

    fixes

[33mcommit 7672456992581b754fcf974bd6a27ef03299c512[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 18 16:39:55 2024 +0530

    updated code

[33mcommit 21bb71e57294817c131fadb22de8e29b30e68eb2[m
Merge: 63e6490 67b1726
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 18 16:20:42 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 63e64901ff5039f02f55d673072acc8a20cbd89c[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 18 16:20:33 2024 +0530

    fixes

[33mcommit 67b17268d5f7122f98a05ec6b9d9505448248511[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 18 16:09:43 2024 +0530

    reset pin is done

[33mcommit a9ebd84687c4df0e09ddebbadf4030c89fb8490e[m
Merge: ed5f85f 7a522bd
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 18 16:06:16 2024 +0530

    fixes

[33mcommit ed5f85f697941fab91f09e2c29762e2f52451c53[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 18 16:05:11 2024 +0530

    add location filter in client list

[33mcommit 7a522bdcb65d6285c49fb690f30c39cd0cc23564[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 18 11:07:09 2024 +0530

    staff creation updated

[33mcommit b500c2891c9e63911dee2e4364117fad36a99626[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 18 09:17:53 2024 +0530

    pin login api

[33mcommit 7e501b66729a2823d45fde8b901637455e96a5c6[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 17 17:04:36 2024 +0530

    updated login api

[33mcommit 2d42f9e549bfa392f0a11a4e943cc3f8f77b284b[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 17 13:25:05 2024 +0530

    resolved issue

[33mcommit acb04713b7bd8078cd22efe2a244c140b1b28d08[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 17 12:56:31 2024 +0530

    added api

[33mcommit 3c6bdd91335e0f2f068d8b4a470d61d32236d7f0[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 16 17:48:27 2024 +0530

    resolved issue

[33mcommit e677614f38b87cec81e16ea7b965b38455f0e8e8[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 16 09:09:38 2024 +0530

    resolved filter

[33mcommit 869ab43ea404216f6a4887a29fe4400f478e5746[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Sep 14 16:09:06 2024 +0530

    resolved bugs

[33mcommit 8be03244158ae0a0957ab9c056046ffb75c1973b[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Sep 14 15:34:44 2024 +0530

    updated constraints

[33mcommit 69bbf72ca0687dcc84f6dbe93091d23141c598a9[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Sep 14 13:33:11 2024 +0530

    added photo

[33mcommit b729e5fc02b31d9fcbbab52fbabb8de98eba366a[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Sep 14 13:24:25 2024 +0530

    updated changes

[33mcommit 5c4caa2cc8e5f44d1522f4fbcf2a8aca6291e2c5[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Sep 14 13:18:50 2024 +0530

    added logo

[33mcommit 8b0ccf162a15ff4199f74d07ba122d7e421abafb[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Sep 14 13:14:38 2024 +0530

    resolved bugs

[33mcommit 57117c06dfc0845771dea93a7ea763a43579b1fb[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Sep 14 10:39:53 2024 +0530

    client update for mobile

[33mcommit ccd8e1b87049b8f33b4dd239b1b285c12177b1b9[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Sep 13 10:58:01 2024 +0530

    issue resolve

[33mcommit 3472669f2fbe9125574af24ebdaf6b458f6855d5[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Sep 13 10:54:15 2024 +0530

    issue resolve

[33mcommit 0287ab27ea88c6b3b3e75ca895c93216515fb64a[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Sep 12 16:10:46 2024 +0530

    updated api

[33mcommit e4c4109ab45f1984bedf8b278493cb08e09f31b2[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Sep 12 15:50:56 2024 +0530

    updated policies api

[33mcommit 6efe686feb865cfaef7b9e185aafb1a8c820117b[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Sep 12 13:59:42 2024 +0530

    updated measurements

[33mcommit 5de8ecf3a300aa17674d92f22f8aa9ff007aa1c5[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Sep 12 13:58:42 2024 +0530

    updated measurements

[33mcommit 94563ab08bb5f7e7a7ad9f178646fcf19c149380[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Sep 12 12:57:07 2024 +0530

    resolved issue

[33mcommit 2c1aa1baf1e92e50706ca005073536dae5b052c2[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Sep 12 11:22:33 2024 +0530

    updated org update api

[33mcommit 7463d4309c3f571467aa45872f2abe1d01e242ef[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 11 17:19:02 2024 +0530

    resolved issue

[33mcommit 2442b0184ca52ba7bb59af3a3b15864814b99549[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 11 16:48:59 2024 +0530

    updated settings

[33mcommit 847e7f1b5d7b1988f8c58956c315b5c9cbacac17[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 11 16:43:32 2024 +0530

    updated details

[33mcommit a986a9d77890c9f2422e8d4fe0dea12ce8f4082e[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 11 16:39:15 2024 +0530

    updated settings api

[33mcommit d82571fd9952d92c35ff6569896446bdb9dc962c[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 10 13:58:12 2024 +0530

    updated org settings

[33mcommit d12ec3c527fafc67cc9456c55f4cce7d77af407e[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 10 13:44:23 2024 +0530

    updated settings

[33mcommit c0eca00eb3f550c2902c278cf0b721b144d132af[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 10 12:13:24 2024 +0530

    amenity delete is done

[33mcommit f822edf420f12c4f82296a597f9cba3a5ba68353[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 9 16:38:08 2024 +0530

    updated clients filter

[33mcommit ff5a30ff934656a90e1d435910f0499dcd9c3feb[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 9 14:03:55 2024 +0530

    added api

[33mcommit 5215b1521ec7b62c14578b2032b01635578f6d35[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 9 13:58:45 2024 +0530

    made changes for client

[33mcommit 54a7cc0162142ce94917d6feaa4532aefc520734[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 9 10:37:59 2024 +0530

    updated code

[33mcommit 072fe8825712e946282ec2531c6eda8e196d5e64[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 9 10:36:08 2024 +0530

    resolved issue

[33mcommit 48368276729fa8a6b3b943800941ed33c3ea2d4b[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 9 10:21:25 2024 +0530

    updated code

[33mcommit 0f48f4f58d88c2c7c81e7773e0cdb70f44e9ad55[m
Merge: 3129448 55e574f
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Sep 6 14:09:01 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 3129448f983999f9fff6c55ba5ab59d96c40a657[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Sep 6 14:08:51 2024 +0530

    fixeS

[33mcommit 55e574f21e3819d1790d5843de4d790eac30acc8[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Sep 6 13:33:04 2024 +0530

    update changes

[33mcommit a337558c4ff2b95fb5dcac354970ca0b2e59de40[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Sep 5 15:27:26 2024 +0530

    updated permission

[33mcommit e1de8e9602f8ebe2e215a04eecd68f8dec5a804b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Sep 5 10:18:18 2024 +0530

    fixes

[33mcommit 98c012cfd7f45424659e1228bd438bca4b492400[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 17:00:34 2024 +0530

    fixes

[33mcommit cf4e23ad930cf0697fdf120e096564a57db4733f[m
Merge: 4b8991a 8ccb2c6
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 16:27:16 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 4b8991a631315bac263f676b88d9abb356562da7[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 16:27:07 2024 +0530

    fixes

[33mcommit 8ccb2c67cb68fd71d803773d14dff2f73dc407d4[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 4 16:21:04 2024 +0530

    client crud updated

[33mcommit 2761c08b5927ce381c77897c0ec8e0c0bd3d9bb6[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 15:43:16 2024 +0530

    fixes

[33mcommit 9de3d3478d26a1cfb6cff80c86b2e88868731aec[m
Merge: 16b15c0 024c703
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 13:53:53 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 16b15c0d17549bf4f6f36095fb0c38b4f4e1d864[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 13:53:44 2024 +0530

    fixes

[33mcommit 024c703c0344c9e9d9fec2922784deb0e81387da[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Sep 4 13:41:25 2024 +0530

    images updated

[33mcommit a8b87f400db68c8334cca5b5e39a6663ccf4d2e3[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 13:28:17 2024 +0530

    fixes

[33mcommit 757b98229d7f63623b696cf6fde6a2288eed6218[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 13:26:10 2024 +0530

    fixes

[33mcommit e2a885ff7777db4f39d92bb6f761ffe23a70bf3b[m
Merge: e413e31 3d4bf90
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 13:19:26 2024 +0530

    fixes

[33mcommit e413e31b9d90b44a47e2ac74d11bcbc54782bb3b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Sep 4 13:18:02 2024 +0530

    fixes

[33mcommit 3d4bf901b3351ab74e2378ff14c9e9428182845f[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 3 17:09:39 2024 +0530

    updated validation

[33mcommit 314bc2ca799c2dd6a0cee72d36522599b9a2a4b6[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 3 16:18:12 2024 +0530

    updated template

[33mcommit 0a04b96fff83500f2e5478c618a9e5519c2105e2[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Sep 3 16:12:54 2024 +0530

    updated org

[33mcommit c1c89b0c6614a15eaee61b8fc8e0136ccc0c0b33[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 17:04:37 2024 +0530

    fixes

[33mcommit 4fb0fb38a4879f1121b22792d4dee034235643ba[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 16:52:47 2024 +0530

    fixes

[33mcommit 24e3529e7dde8b48c6371d60e5513957161862bd[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 16:43:25 2024 +0530

    fixes

[33mcommit ae259379c6fa3920e2b1656f66c8085749fe6c1f[m
Merge: b18c0fe f776fd4
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 16:27:23 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit b18c0fea0eae7fe61b5e03767a4b40bbfd36f84b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 16:27:08 2024 +0530

    fixes

[33mcommit f776fd41a6cd0ee33d8a66b0b9e047f3c8531167[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 2 15:56:29 2024 +0530

    updated errors

[33mcommit 87013040f38dfa1157ceaa1e3f229be155bf655b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 15:44:45 2024 +0530

    add city api

[33mcommit ae90c203f09849c1661f9a19ab33e30d0806074f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 15:34:19 2024 +0530

    fixes

[33mcommit 39b16802dfd6f544ee0e86b799e911c8276fd460[m
Merge: f53b87a 61a3bd2
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 14:07:48 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit f53b87a6321789792145ea549efab045720d2136[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Sep 2 14:07:36 2024 +0530

    remove conditions

[33mcommit 61a3bd2f3cfb3a3b7f8bfc9c778ddafbab0a7810[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 2 12:21:33 2024 +0530

    update changes

[33mcommit 4398431107de41f9bf6b89537c14d2efbfbeaac3[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 2 11:48:44 2024 +0530

    updated attributes

[33mcommit 8fef01ebbd3ff4eb043a688c99d1f7c41ad9a3ed[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 2 10:57:16 2024 +0530

    updated code

[33mcommit d309038c50b248da586e3898f4cff209e327854a[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Sep 2 10:53:32 2024 +0530

    clients crud

[33mcommit 469c1477ab62ac0f9e84e4e5806f95710bcd63e7[m
Author: Shivam Monga <<EMAIL>>
Date:   Sat Aug 31 16:47:45 2024 +0530

    clients crud

[33mcommit 37bd892716ecbb16bc28bc33b74f9928e1a2599a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 30 16:15:14 2024 +0530

    make password-rest api

[33mcommit fd9451dce6699b148842f46058a9272e47fee447[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 29 17:03:16 2024 +0530

    fixes

[33mcommit 166fe9f65d44480a7a3646bb1a0beddb6e0f42f0[m
Author: hksmanpower25 <<EMAIL>>
Date:   Thu Aug 29 11:17:55 2024 +0000

    get details by id

[33mcommit 00d88baf41d13ffeac8ad0191e90bdbd638d52a9[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Aug 29 16:44:41 2024 +0530

    org update done

[33mcommit 7841bcca42c90cf9d513cc669562a4f098c9d3e0[m
Author: hksmanpower25 <<EMAIL>>
Date:   Thu Aug 29 11:07:33 2024 +0000

    fix endpoint anme

[33mcommit 3728717ef694835fd4d1a3ec87254b2895920a9a[m
Merge: 5f730de 173af79
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 29 16:21:23 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 5f730dedeced803594a148fc34d683872ad08696[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 29 16:21:10 2024 +0530

    add staff list api

[33mcommit 173af79b3c8da7ba96b9f889358873db511c3f68[m
Merge: a950f9a cf87bbf
Author: Shivam Monga <<EMAIL>>
Date:   Thu Aug 29 10:54:56 2024 +0530

    merged with feature/rbac

[33mcommit cf87bbfc9950090f50e787c98343a6e552b228d6[m[33m ([m[1;31morigin/feature/rbac[m[33m)[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Aug 29 10:54:11 2024 +0530

    organization details api

[33mcommit 2565ee6570ecace2d2c68d3e0e05b8f6301ba741[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 28 17:06:06 2024 +0530

    resolved issues

[33mcommit 1d7992578460ef136fb88e390d465b5ad75bfa11[m
Merge: 81d8759 a950f9a
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 28 16:51:33 2024 +0530

    merged with master

[33mcommit a950f9a046811dcdb554fbe99cbc438125df7ff9[m
Merge: b64944b 81d8759
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 28 16:50:57 2024 +0530

    merged with feature/rbac

[33mcommit 81d8759bf42ccc93ffa865cc9f4eb3eb161de59b[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 28 16:50:08 2024 +0530

    amenities, facility, organization is completed

[33mcommit b64944be32ed17e32c2c2979fd376b1718a8b53f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Aug 28 14:13:18 2024 +0530

    fix payload

[33mcommit 31ce18430453eaac54c79b553bb84bcc6a302c42[m
Author: hksmanpower25 <<EMAIL>>
Date:   Wed Aug 28 07:56:07 2024 +0000

    add delete api

[33mcommit ee882e12833c794a5ae8618991c77f8a1f41c5b9[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Aug 28 13:20:14 2024 +0530

    fixes

[33mcommit 8f3ff48e2949ddffa7368c7f76e15ce9b7b149ee[m
Merge: 0038135 226ef26
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Aug 28 13:02:32 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 0038135bb021349153dae1ba0b067f85af35ff00[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Aug 28 13:02:20 2024 +0530

    create new api for staff

[33mcommit 73498f73c6845cba1843c04c26c40d3a531b7275[m
Merge: 45c2fda 226ef26
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 28 12:56:35 2024 +0530

    merged with master

[33mcommit 226ef2663b4ae6f2f9bd434b31060f837e1de216[m
Merge: 4b12594 45c2fda
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 28 12:32:22 2024 +0530

    merged with feature/rbac

[33mcommit 45c2fda7cfb1f5f1151f8b7a41e5266072975925[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 28 12:31:33 2024 +0530

    updated facility crud

[33mcommit 4b12594bc285c7c16d5ed3ecc4e4ef7d2d54e7d2[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Aug 27 18:08:25 2024 +0530

    fixes

[33mcommit 121b75b7b602cf3151bb3e611a6f6ee1d24e5b9b[m
Merge: f035e01 213eaf7
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Aug 27 16:22:42 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit f035e018a2102bd63565e6674a895af331fc096f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Aug 27 16:22:24 2024 +0530

    fixes

[33mcommit e299c379d4634db1d7cce5381e50c6e0f924ca60[m
Merge: 2bd55ce 213eaf7
Author: Shivam Monga <<EMAIL>>
Date:   Tue Aug 27 13:33:26 2024 +0530

    merged with feature/rbac

[33mcommit 213eaf7b7f24cbbb7834bf0e1768dca83db050f1[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Aug 27 13:24:00 2024 +0530

    removed console

[33mcommit 2530bcc351ac7fd5a2fcc103ab1054c5a942fba0[m
Author: Shivam Monga <<EMAIL>>
Date:   Tue Aug 27 13:08:18 2024 +0530

    organization apis

[33mcommit 207846a9902a0cdac555ce1f742b5c8eba9245bf[m
Merge: fdb18ef dd2a1d4
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Aug 27 13:00:30 2024 +0530

    fix conflicts

[33mcommit fdb18ef16a3845139f913a933d9895bcbb192eb7[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Aug 27 12:57:06 2024 +0530

    add staff creation api

[33mcommit dd2a1d4b674789741a02a0dd7b3b1cefc0350db8[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 26 16:28:38 2024 +0530

    updated code

[33mcommit da1a2638855b7d4e771706f33b6539cfd3d389ce[m
Merge: 3850e7d 2bd55ce
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 26 16:08:34 2024 +0530

    merged with feature/rbac

[33mcommit 2bd55ce05d3e555f66454255b79942291e495528[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 26 16:07:31 2024 +0530

    organization create and listing

[33mcommit 3850e7d32d91cf6f959620ddbc40fca2cf440a6b[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 26 14:07:51 2024 +0530

    organization setup

[33mcommit 0487201d454e769df931e6d192d4eb8443900e08[m
Merge: fa6640b ddb6810
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 26 12:18:07 2024 +0530

    merged with feature/rbac

[33mcommit ddb68105d959aacf67d87e1721f9f176557262b2[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 26 12:17:05 2024 +0530

    updated roles

[33mcommit a7cbf9008ffb13d93037b5a093b885d1acdc86a3[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 26 12:12:10 2024 +0530

    updated roles

[33mcommit fa6640bca013050be6d58c6534f7aa05eb68c756[m
Merge: 20192ed d37c00f
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 23 13:00:56 2024 +0530

    Merge branch 'feature/rbac' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit d37c00f12c9b46094e98d181858ee1f208e31de9[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Aug 23 12:28:06 2024 +0530

    updated

[33mcommit 3f1f10ce14db5c2874e6e8c10211802e724cd681[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Aug 23 12:20:08 2024 +0530

    updated code

[33mcommit 20192ede30229d29db6b1999c0b31fa18627a233[m
Merge: ce5bb21 1b9d644
Author: Shivam Monga <<EMAIL>>
Date:   Thu Aug 22 16:29:33 2024 +0530

    merged with feature/rbac

[33mcommit 1b9d6442a4869cb6051af5bf3202e811ab68752c[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Aug 22 16:28:23 2024 +0530

    gym, attribute and amenities crud is done

[33mcommit cdefac14867a6761b37d13569ae4c0844d7c632d[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 21 15:21:14 2024 +0530

    amenities crud is done

[33mcommit ce5bb2171c9499c21b61fdbcd6ac5b07d0e1ea0b[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 14 13:47:32 2024 +0530

    resolved issues

[33mcommit 1503c8f67d5a09bbb494cd1bf6a9d84bbd7a938d[m
Merge: 9fb7dd6 156d889
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 14 13:46:03 2024 +0530

    merged with feature/trainer-onboarding

[33mcommit 156d889afbc10a8ce5977005797c9d5f17905277[m[33m ([m[1;31morigin/feature/trainer-onboarding[m[33m)[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 14 13:44:44 2024 +0530

    updated code

[33mcommit 9fb7dd6a2d30932942809d4232a601d6b74184f5[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 9 09:58:05 2024 +0530

    fixes

[33mcommit 63b7fd56f3dc5fb0bf68fe151ce47415f53ce5cf[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 9 09:30:50 2024 +0530

    add custom messages in dto

[33mcommit 8d74bdd71b506babc106f5ca2bd0373b3a226fb5[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 18:22:23 2024 +0530

    fix dto

[33mcommit 3bf9599632ffcac7c8a8bc3a664a2e537d8fa83d[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 16:50:47 2024 +0530

    cange type in dto

[33mcommit 3e12145a4f25f5a9eff76fc540766c07d546ea90[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 16:44:22 2024 +0530

    fixes

[33mcommit a5c02c9f2d8cc92b9398ad700ea068e0c779ee54[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 16:36:15 2024 +0530

    modify lookup query

[33mcommit 284b6bc275876adf8b9708626f4ef56e252e8e43[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 16:24:35 2024 +0530

    add amenities

[33mcommit 56410fb98d4e926ffeb45d7bce6b85fa1a3df4dd[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 15:45:34 2024 +0530

    add cities api

[33mcommit 600dfe91eb0af2841e0b238205672de45cf4b504[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 15:20:49 2024 +0530

    add states api

[33mcommit d66a70ca01be22897f62542dca5df7bee4f5cedb[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 13:44:18 2024 +0530

    fixes

[33mcommit 31db99c9f0262d5ed9d3a95dafbdc66baedcea22[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 12:12:37 2024 +0530

    fix forgot password api

[33mcommit a196231a23daf864d40b6b13cd30890c5b42068b[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 8 11:50:31 2024 +0530

    fixes

[33mcommit 36bd7dee8909942d4a5a22af5a99b59afbbb84f2[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Aug 7 17:12:46 2024 +0530

    fix api tags for admin

[33mcommit f4780f6bc097ccf598de0c489f1088fa5f9ad07a[m
Merge: a37aa5b 4de299a
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 7 17:03:32 2024 +0530

    merged with feature/onboarding

[33mcommit a37aa5b9a053ff21486a2a549f2f10212d54d271[m
Merge: d0c171e 9dac4dc
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Aug 7 17:01:58 2024 +0530

    merge conflicts

[33mcommit d0c171e576f8a48b6db3fe68a2a8528f2a078a7f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Aug 7 17:00:09 2024 +0530

    add gym modile

[33mcommit 4de299a9c621c11757f3578b3bc7b1368e1ee53d[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 7 16:56:38 2024 +0530

    attributes crud is done

[33mcommit 9dac4dc2c60ff5e2c922c814f06ff5b4a5f2f2d3[m
Merge: 6894543 aa05859
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 7 12:44:19 2024 +0530

    merged with feature/onboarding

[33mcommit aa05859df059c626fef1e35e65b7909c1ca222d5[m
Merge: ac964fb 6894543
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 7 12:43:25 2024 +0530

    merged with master

[33mcommit ac964fb7f870c0679334267ffc92f0ee90467a76[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 7 12:40:53 2024 +0530

    category crud is completed

[33mcommit 6894543d3af52ab88d7482b57b2af85552347b1d[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Aug 7 11:11:02 2024 +0530

    add gym module and mail module

[33mcommit c413eca8b118f333845d9c44f895b9340df895aa[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Aug 7 10:51:11 2024 +0530

    updated apis

[33mcommit 9e37c2233a90673fac9a9690d547c2ebe3578c8f[m
Author: shivam  Gupta <<EMAIL>>
Date:   Tue Aug 6 11:03:15 2024 +0530

    fix onboard user

[33mcommit 26436e9a8ea9f954669573b5c449934f5e5c3f87[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Aug 5 19:03:15 2024 +0530

    add upload profile picture function

[33mcommit f33106ea264a1345a33382a0f1249a0b99f9fa7f[m
Merge: 2bee648 f9764f9
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 5 18:26:27 2024 +0530

    merged with master

[33mcommit 2bee6481a6266ee3ae75178e9c06a3ab45729d9a[m
Author: Shivam Monga <<EMAIL>>
Date:   Mon Aug 5 18:23:45 2024 +0530

    trainer onboarding

[33mcommit f9764f9e1ed80838b62e9a627431508e47ff66c4[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Aug 5 17:06:26 2024 +0530

    add admin-user apis

[33mcommit b3aa1a4fc62b4b216486ebb303d34aba76e02aa3[m
Author: shivam  Gupta <<EMAIL>>
Date:   Mon Aug 5 13:23:45 2024 +0530

    added clientId

[33mcommit 4de28760ff64da0063e80289707695b75d83da15[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 2 21:42:32 2024 +0530

    create new api to onboard user

[33mcommit 8d226b37994c2dd3180b13cb1b065a90b27ca5e6[m
Merge: a0b41f0 fdca633
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 2 18:29:22 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit a0b41f0f6d027c1afc4a2cce93397d99c0de6dc5[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 2 18:29:14 2024 +0530

    modify dto

[33mcommit fdca6336094f0b4ef7e75f8697afafa84f782b89[m
Author: Shivam Monga <<EMAIL>>
Date:   Fri Aug 2 18:28:29 2024 +0530

    updated code

[33mcommit e6a04262902613276935ea55052a7eba4e351119[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 2 18:12:19 2024 +0530

    added dto for user creation

[33mcommit 18d6dc4d815a1598c8b53b9148700ed4210dafc8[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 2 15:25:52 2024 +0530

    remove city from user schema

[33mcommit 1386cdf3b7981a88ccca378b2476bd5370fe6cb9[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 2 14:10:18 2024 +0530

    Enhancement in auth logic

[33mcommit 00c65215f14a296691bc05da2139c50116f548f6[m
Author: shivam  Gupta <<EMAIL>>
Date:   Fri Aug 2 11:14:47 2024 +0530

    make user api and fix authentication

[33mcommit 7c2df98fcd4063b75d25507f84fc81612157206d[m
Merge: 1e8f9b8 01685cc
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 1 16:42:04 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit 1e8f9b897d5bed886ba91767eb6ae61db2064159[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 1 16:41:52 2024 +0530

    add valadations in dto

[33mcommit 01685cc9c64efd6564e7208df8abe651dd52da20[m
Author: Shivam Monga <<EMAIL>>
Date:   Thu Aug 1 13:08:22 2024 +0530

    updated code

[33mcommit f0276a3775279d53285ae8abcd66cd050a81b7c3[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 1 13:04:51 2024 +0530

    add transaction and try catch and valadation

[33mcommit fdf69b8abace00303bc4e2c65adc9b0b5d436a73[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 1 11:31:49 2024 +0530

    logic enhancement for chnage password

[33mcommit 0bf17d911c6fb6f1f0376eb6a1089fbcab6b65e6[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 1 11:16:16 2024 +0530

    fix auth functions

[33mcommit b873763a2bd72c5ae24ef4cd0635e52e4c8325f8[m
Author: shivam  Gupta <<EMAIL>>
Date:   Thu Aug 1 10:59:43 2024 +0530

    fixes in auth module

[33mcommit 203f0ad0f6299ee0ed7064a1c2799b3d5daea6fc[m
Merge: b751339 73da483
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jul 31 19:02:50 2024 +0530

    fix conflicts

[33mcommit b7513390d212351e09f9bef6b92763dda2e46409[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jul 31 19:00:16 2024 +0530

    fix main.ts file

[33mcommit 73da483f9f45441a0d60511fb73e9dd9023be46e[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Jul 31 18:13:00 2024 +0530

    removed unused code

[33mcommit 9c638b91d1c0befeafababcf75fde0557978ce0d[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Jul 31 18:11:30 2024 +0530

    updated code

[33mcommit da30f2198825b2350473f4d992492621cb3aed99[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Jul 31 17:55:31 2024 +0530

    updated code

[33mcommit b05cb392a5a9e7b1ce41bb0b4e1ee134f5c19f5a[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jul 31 17:53:10 2024 +0530

    add swagger

[33mcommit 3e972ea57789bd0c737982ca6a7acdd1c02aaa14[m
Merge: a9f4900 4068d10
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jul 31 13:21:38 2024 +0530

    Merge branch 'master' of https://github.com/HKS-Manpower/gym-nestjs

[33mcommit a9f490025c84f536494a298cd1849d6ecbcc1925[m
Author: shivam  Gupta <<EMAIL>>
Date:   Wed Jul 31 13:08:30 2024 +0530

    correct spellings

[33mcommit 4068d100d0a2e5d026b92902ebcdfacfa9f35945[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Jul 31 13:05:13 2024 +0530

    updated code

[33mcommit e46700a13c1778163648726807bf55b7917cad39[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Jul 31 12:55:52 2024 +0530

    updated formatting

[33mcommit 5a1134946900744490845bf2d8f5460cf0684d29[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Jul 31 12:54:10 2024 +0530

    updated code

[33mcommit ee9235a40b569e0ea420b87c8f00386a99b25f77[m
Author: Shivam Monga <<EMAIL>>
Date:   Wed Jul 31 12:51:56 2024 +0530

    updated code

[33mcommit 7e66b2fdbac2f67ba671e4c1e7f8aa1e5f8b6c79[m
Author: shivammonga <<EMAIL>>
Date:   Wed Jul 31 11:31:34 2024 +0530

    Initial commit
